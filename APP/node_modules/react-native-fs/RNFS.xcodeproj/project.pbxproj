// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		645644281EB8DAA100672408 /* NSArray+Map.m in Sources */ = {isa = PBXBuildFile; fileRef = F1EB08BA1AFD0E6A008F8F2B /* NSArray+Map.m */; };
		645644291EB8DAA100672408 /* RNFSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F1E59BDE1ADD662800ACA28A /* RNFSManager.m */; };
		6456442A1EB8DAA100672408 /* Downloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BF740761C033A2E0057A1E7 /* Downloader.m */; };
		6456442B1EB8DAA100672408 /* Uploader.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BB364CB1CDA130000435A01 /* Uploader.m */; };
		8BB364CC1CDA130000435A01 /* Uploader.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BB364CB1CDA130000435A01 /* Uploader.m */; };
		8BF740771C033A2E0057A1E7 /* Downloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 8BF740761C033A2E0057A1E7 /* Downloader.m */; };
		F1E59BDF1ADD662800ACA28A /* RNFSManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F1E59BDE1ADD662800ACA28A /* RNFSManager.m */; };
		F1EB08BB1AFD0E6A008F8F2B /* NSArray+Map.m in Sources */ = {isa = PBXBuildFile; fileRef = F1EB08BA1AFD0E6A008F8F2B /* NSArray+Map.m */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		6456441D1EB8DA9100672408 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F12AFB991ADAF8F800E0535D /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		6456441F1EB8DA9100672408 /* libRNFS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNFS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		8BB364CA1CDA130000435A01 /* Uploader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Uploader.h; sourceTree = "<group>"; };
		8BB364CB1CDA130000435A01 /* Uploader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Uploader.m; sourceTree = "<group>"; };
		8BF740751C033A2E0057A1E7 /* Downloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Downloader.h; sourceTree = "<group>"; };
		8BF740761C033A2E0057A1E7 /* Downloader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Downloader.m; sourceTree = "<group>"; };
		F12AFB9B1ADAF8F800E0535D /* libRNFS.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libRNFS.a; sourceTree = BUILT_PRODUCTS_DIR; };
		F1E59BDD1ADD662800ACA28A /* RNFSManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RNFSManager.h; sourceTree = "<group>"; };
		F1E59BDE1ADD662800ACA28A /* RNFSManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RNFSManager.m; sourceTree = "<group>"; };
		F1EB08B91AFD0E6A008F8F2B /* NSArray+Map.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSArray+Map.h"; sourceTree = "<group>"; };
		F1EB08BA1AFD0E6A008F8F2B /* NSArray+Map.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSArray+Map.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6456441C1EB8DA9100672408 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F12AFB981ADAF8F800E0535D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F12AFB921ADAF8F800E0535D = {
			isa = PBXGroup;
			children = (
				F1EB08B91AFD0E6A008F8F2B /* NSArray+Map.h */,
				F1EB08BA1AFD0E6A008F8F2B /* NSArray+Map.m */,
				F1E59BDD1ADD662800ACA28A /* RNFSManager.h */,
				F1E59BDE1ADD662800ACA28A /* RNFSManager.m */,
				8BF740751C033A2E0057A1E7 /* Downloader.h */,
				8BF740761C033A2E0057A1E7 /* Downloader.m */,
				8BB364CA1CDA130000435A01 /* Uploader.h */,
				8BB364CB1CDA130000435A01 /* Uploader.m */,
				F12AFB9C1ADAF8F800E0535D /* Products */,
			);
			sourceTree = "<group>";
		};
		F12AFB9C1ADAF8F800E0535D /* Products */ = {
			isa = PBXGroup;
			children = (
				F12AFB9B1ADAF8F800E0535D /* libRNFS.a */,
				6456441F1EB8DA9100672408 /* libRNFS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6456441E1EB8DA9100672408 /* RNFS-tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 645644271EB8DA9100672408 /* Build configuration list for PBXNativeTarget "RNFS-tvOS" */;
			buildPhases = (
				6456441B1EB8DA9100672408 /* Sources */,
				6456441C1EB8DA9100672408 /* Frameworks */,
				6456441D1EB8DA9100672408 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "RNFS-tvOS";
			productName = "RNFS-tvOS";
			productReference = 6456441F1EB8DA9100672408 /* libRNFS.a */;
			productType = "com.apple.product-type.library.static";
		};
		F12AFB9A1ADAF8F800E0535D /* RNFS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F12AFBAF1ADAF8F800E0535D /* Build configuration list for PBXNativeTarget "RNFS" */;
			buildPhases = (
				F12AFB971ADAF8F800E0535D /* Sources */,
				F12AFB981ADAF8F800E0535D /* Frameworks */,
				F12AFB991ADAF8F800E0535D /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = RNFS;
			productName = RNLocalNotification;
			productReference = F12AFB9B1ADAF8F800E0535D /* libRNFS.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F12AFB931ADAF8F800E0535D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0630;
				ORGANIZATIONNAME = "Johannes Lumpe";
				TargetAttributes = {
					6456441E1EB8DA9100672408 = {
						CreatedOnToolsVersion = 8.3.2;
						ProvisioningStyle = Automatic;
					};
					F12AFB9A1ADAF8F800E0535D = {
						CreatedOnToolsVersion = 6.3;
					};
				};
			};
			buildConfigurationList = F12AFB961ADAF8F800E0535D /* Build configuration list for PBXProject "RNFS" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = F12AFB921ADAF8F800E0535D;
			productRefGroup = F12AFB9C1ADAF8F800E0535D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F12AFB9A1ADAF8F800E0535D /* RNFS */,
				6456441E1EB8DA9100672408 /* RNFS-tvOS */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		6456441B1EB8DA9100672408 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				645644281EB8DAA100672408 /* NSArray+Map.m in Sources */,
				645644291EB8DAA100672408 /* RNFSManager.m in Sources */,
				6456442A1EB8DAA100672408 /* Downloader.m in Sources */,
				6456442B1EB8DAA100672408 /* Uploader.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F12AFB971ADAF8F800E0535D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F1E59BDF1ADD662800ACA28A /* RNFSManager.m in Sources */,
				8BB364CC1CDA130000435A01 /* Uploader.m in Sources */,
				F1EB08BB1AFD0E6A008F8F2B /* NSArray+Map.m in Sources */,
				8BF740771C033A2E0057A1E7 /* Downloader.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		645644251EB8DA9100672408 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_TESTABILITY = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNFS;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Debug;
		};
		645644261EB8DA9100672408 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = RNFS;
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Release;
		};
		F12AFBAD1ADAF8F800E0535D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		F12AFBAE1ADAF8F800E0535D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F12AFBB01ADAF8F800E0535D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		F12AFBB11ADAF8F800E0535D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../React/**",
					"$(SRCROOT)/../react-native/React/**",
					"$(SRCROOT)/node_modules/react-native/React/**",
				);
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		645644271EB8DA9100672408 /* Build configuration list for PBXNativeTarget "RNFS-tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				645644251EB8DA9100672408 /* Debug */,
				645644261EB8DA9100672408 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
		};
		F12AFB961ADAF8F800E0535D /* Build configuration list for PBXProject "RNFS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F12AFBAD1ADAF8F800E0535D /* Debug */,
				F12AFBAE1ADAF8F800E0535D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F12AFBAF1ADAF8F800E0535D /* Build configuration list for PBXNativeTarget "RNFS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F12AFBB01ADAF8F800E0535D /* Debug */,
				F12AFBB11ADAF8F800E0535D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F12AFB931ADAF8F800E0535D /* Project object */;
}
