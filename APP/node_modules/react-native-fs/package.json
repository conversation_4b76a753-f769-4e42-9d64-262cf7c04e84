{"name": "react-native-fs", "version": "2.20.0", "description": "Native filesystem access for react-native", "main": "FS.common.js", "typings": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "flow": "flow; test $? -eq 0 -o $? -eq 2"}, "repository": {"type": "git", "url": "**************:itinance/react-native-fs.git"}, "keywords": ["react-component", "react-native", "ios", "android", "fs", "filesystem", "download", "upload", "file-transfer"], "author": "<PERSON> <<EMAIL>> (https://github.com/johanneslumpe)", "license": "MIT", "dependencies": {"base-64": "^0.1.0", "utf8": "^3.0.0"}, "devDependencies": {"create-react-class": "15.6.3", "flow-bin": "^0.109.0", "prop-types": "^15.7.2"}, "peerDependencies": {"react-native": "*", "react-native-windows": "*"}, "peerDependenciesMeta": {"react-native-windows": {"optional": true}}}