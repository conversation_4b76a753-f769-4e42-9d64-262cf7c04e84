{"-W093": true, "asi": false, "bitwise": true, "boss": false, "browser": false, "camelcase": true, "couch": false, "curly": true, "debug": false, "devel": true, "dojo": false, "eqeqeq": true, "eqnull": false, "esnext": true, "evil": false, "expr": true, "forin": false, "freeze": true, "funcscope": true, "gcl": false, "globalstrict": true, "immed": false, "indent": 2, "iterator": false, "jquery": false, "lastsemic": false, "latedef": false, "laxbreak": true, "laxcomma": false, "loopfunc": false, "maxcomplexity": false, "maxdepth": false, "maxerr": 50, "maxlen": 80, "maxparams": false, "maxstatements": false, "mootools": false, "moz": false, "multistr": false, "newcap": true, "noarg": true, "node": true, "noempty": true, "nonbsp": true, "nonew": true, "nonstandard": false, "notypeof": false, "noyield": false, "phantom": false, "plusplus": false, "predef": ["jasmine", "describe", "beforeEach", "it", "jest", "pit", "expect", "rootRequire"], "proto": false, "prototypejs": false, "quotmark": true, "rhino": false, "scripturl": false, "shadow": false, "smarttabs": false, "strict": true, "sub": false, "supernew": false, "trailing": true, "undef": true, "unused": true, "validthis": false, "worker": false, "wsh": false, "yui": false}