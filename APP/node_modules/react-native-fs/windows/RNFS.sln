Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.26730.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RNFS", "RNFs\RNFS.csproj", "{746610D0-8693-11E7-A20D-BF83F7366778}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ReactNative", "..\node_modules\react-native-windows\ReactWindows\ReactNative\ReactNative.csproj", "{C7673AD5-E3AA-468C-A5FD-FA38154E205C}"
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "ReactNative.Shared", "..\node_modules\react-native-windows\ReactWindows\ReactNative.Shared\ReactNative.Shared.shproj", "{EEA8B852-4D07-48E1-8294-A21AB5909FE5}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ChakraBridge", "..\node_modules\react-native-windows\ReactWindows\ChakraBridge\ChakraBridge.vcxproj", "{4B72C796-16D5-4E3A-81C0-3E36F531E578}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RNFS.Tests", "RNFS.Tests\RNFS.Tests.csproj", "{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RNFS.Net46", "RNFS.Net46\RNFS.Net46.csproj", "{8F7EE18F-8E79-4648-B442-9554443BE262}"
EndProject
Global
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		..\node_modules\react-native-windows\ReactWindows\ReactNative.Shared\ReactNative.Shared.projitems*{c7673ad5-e3aa-468c-a5fd-fa38154e205c}*SharedItemsImports = 4
		..\node_modules\react-native-windows\Yoga\csharp\Facebook.Yoga\Facebook.Yoga.Shared.projitems*{c7673ad5-e3aa-468c-a5fd-fa38154e205c}*SharedItemsImports = 4
		..\node_modules\react-native-windows\ReactWindows\ReactNative.Shared\ReactNative.Shared.projitems*{eea8b852-4d07-48e1-8294-a21ab5909fe5}*SharedItemsImports = 13
	EndGlobalSection
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|ARM = Debug|ARM
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Development|ARM = Development|ARM
		Development|x64 = Development|x64
		Development|x86 = Development|x86
		Release|ARM = Release|ARM
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|ARM.ActiveCfg = Debug|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|ARM.Build.0 = Debug|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|x64.ActiveCfg = Debug|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|x64.Build.0 = Debug|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|x86.ActiveCfg = Debug|x86
		{746610D0-8693-11E7-A20D-BF83F7366778}.Debug|x86.Build.0 = Debug|x86
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|ARM.ActiveCfg = Development|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|ARM.Build.0 = Development|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|x64.ActiveCfg = Development|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|x64.Build.0 = Development|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|x86.ActiveCfg = Development|x86
		{746610D0-8693-11E7-A20D-BF83F7366778}.Development|x86.Build.0 = Development|x86
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|ARM.ActiveCfg = Release|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|ARM.Build.0 = Release|ARM
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|x64.ActiveCfg = Release|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|x64.Build.0 = Release|x64
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|x86.ActiveCfg = Release|x86
		{746610D0-8693-11E7-A20D-BF83F7366778}.Release|x86.Build.0 = Release|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|ARM.ActiveCfg = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|ARM.Build.0 = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x64.ActiveCfg = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x64.Build.0 = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x86.ActiveCfg = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Debug|x86.Build.0 = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|ARM.ActiveCfg = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|ARM.Build.0 = Debug|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|x64.ActiveCfg = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|x64.Build.0 = Debug|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|x86.ActiveCfg = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Development|x86.Build.0 = Debug|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|ARM.ActiveCfg = Release|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|ARM.Build.0 = Release|ARM
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x64.ActiveCfg = Release|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x64.Build.0 = Release|x64
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x86.ActiveCfg = Release|x86
		{C7673AD5-E3AA-468C-A5FD-FA38154E205C}.Release|x86.Build.0 = Release|x86
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|ARM.ActiveCfg = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|ARM.Build.0 = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x64.ActiveCfg = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x64.Build.0 = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x86.ActiveCfg = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Debug|x86.Build.0 = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|ARM.ActiveCfg = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|ARM.Build.0 = Debug|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|x64.ActiveCfg = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|x64.Build.0 = Debug|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|x86.ActiveCfg = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Development|x86.Build.0 = Debug|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|ARM.ActiveCfg = Release|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|ARM.Build.0 = Release|ARM
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x64.ActiveCfg = Release|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x64.Build.0 = Release|x64
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x86.ActiveCfg = Release|Win32
		{4B72C796-16D5-4E3A-81C0-3E36F531E578}.Release|x86.Build.0 = Release|Win32
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|ARM.ActiveCfg = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|ARM.Build.0 = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|ARM.Deploy.0 = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x64.ActiveCfg = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x64.Build.0 = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x64.Deploy.0 = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x86.ActiveCfg = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x86.Build.0 = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Debug|x86.Deploy.0 = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|ARM.ActiveCfg = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|ARM.Build.0 = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|ARM.Deploy.0 = Debug|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x64.ActiveCfg = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x64.Build.0 = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x64.Deploy.0 = Debug|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x86.ActiveCfg = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x86.Build.0 = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Development|x86.Deploy.0 = Debug|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|ARM.ActiveCfg = Release|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|ARM.Build.0 = Release|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|ARM.Deploy.0 = Release|ARM
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x64.ActiveCfg = Release|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x64.Build.0 = Release|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x64.Deploy.0 = Release|x64
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x86.ActiveCfg = Release|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x86.Build.0 = Release|x86
		{8D2229AC-F6EC-4FBD-9AAC-FE4792DA98C6}.Release|x86.Deploy.0 = Release|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|ARM.ActiveCfg = Debug|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|ARM.Build.0 = Debug|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|x64.ActiveCfg = Debug|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|x64.Build.0 = Debug|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|x86.ActiveCfg = Debug|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Debug|x86.Build.0 = Debug|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|ARM.ActiveCfg = Development|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|ARM.Build.0 = Development|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|x64.ActiveCfg = Development|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|x64.Build.0 = Development|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|x86.ActiveCfg = Development|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Development|x86.Build.0 = Development|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|ARM.ActiveCfg = Release|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|ARM.Build.0 = Release|ARM
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|x64.ActiveCfg = Release|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|x64.Build.0 = Release|x64
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|x86.ActiveCfg = Release|x86
		{8F7EE18F-8E79-4648-B442-9554443BE262}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FBB6610F-6707-4A34-9D3B-3E0709F7822A}
	EndGlobalSection
EndGlobal
