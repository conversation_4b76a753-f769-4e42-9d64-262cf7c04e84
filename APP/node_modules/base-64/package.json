{"name": "base-64", "version": "0.1.0", "description": "A robust base64 encoder/decoder that is fully compatible with `atob()` and `btoa()`, written in JavaScript.", "homepage": "http://mths.be/base64", "main": "base64.js", "keywords": ["codec", "decoder", "encoder", "base64", "atob", "btoa"], "licenses": [{"type": "MIT", "url": "http://mths.be/mit"}], "author": {"name": "<PERSON>", "url": "http://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/base64.git"}, "bugs": {"url": "https://github.com/mathiasbynens/base64/issues"}, "files": ["LICENSE-MIT.txt", "base64.js"], "directories": {"test": "tests"}, "scripts": {"test": "node tests/tests.js"}, "devDependencies": {"grunt": "~0.4.4", "grunt-shell": "~0.7.0", "grunt-template": "~0.2.3", "istanbul": "~0.2.7", "qunit-extras": "~1.1.0", "qunitjs": "~1.11.0", "requirejs": "~2.1.11"}}