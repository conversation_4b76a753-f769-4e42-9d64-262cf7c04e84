//
//  RNSharedElementTransition.h
//  react-native-shared-element
//

#ifndef RNSharedElementTransition_h
#define RNSharedElementTransition_h

#import <React/RCTView.h>
#import <UIKit/UIKit.h>
#import "RNSharedElementNodeManager.h"
#import "RNSharedElementDelegate.h"

@interface RNSharedElementTransition : UIView <RNSharedElementDelegate>

@property (nonatomic, assign) CGFloat nodePosition;
@property (nonatomic, assign) RNSharedElementAnimation animation;
@property (nonatomic, assign) RNSharedElementResize resize;
@property (nonatomic, assign) RNSharedElementAlign align;
@property (nonatomic, strong) RNSharedElementNode* startNode;
@property (nonatomic, strong) RNSharedElementNode* startAncestor;
@property (nonatomic, copy) RCTDirectEventBlock onMeasureNode;
@property (nonatomic, strong) RNSharedElementNode* endNode;
@property (nonatomic, strong) RNSharedElementNode* endAncestor;

- (instancetype)initWithNodeManager:(RNSharedElementNodeManager*)nodeManager;

@end

#endif
