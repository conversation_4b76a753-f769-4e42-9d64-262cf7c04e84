{"name": "react-native-shared-element", "version": "0.8.4", "description": "Native shared element transition primitives for react-native 💫", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare_disabled": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "copy-flow-typings": "mkdir -p ./build && cp ./flow-typed/index.js.flow ./build/index.js.flow", "prepare": "yarn copy-flow-typings"}, "keywords": ["react-native-shared-element", "expo", "expo-shared-element", "react-native", "magic move", "shared element", "shared element transition", "visual clone"], "repository": "https://github.com/IjzerenHein/react-native-shared-element", "bugs": "https://github.com/IjzerenHein/react-native-shared-element/issues", "author": "IjzerenHein <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/IjzerenHein/react-native-shared-element", "files": ["src/", "build/", "/ios", "/*.podspec", "/android", "/windows", "/web"], "devDependencies": {"@types/react": "^17.0.38", "@types/react-native": "^0.66.11", "eslint": "7", "expo-module-scripts": "^2.0.0", "prettier": "^2.5.1"}, "dependencies": {}}