import * as React from "react";
import { View, Text, Animated, Dimensions, StyleSheet, processColor, Platform, } from "react-native";
import { RNSharedElementTransitionView } from "./RNSharedElementTransitionView";
const NativeAnimationType = new Map([
    ["move", 0],
    ["fade", 1],
    ["fade-in", 2],
    ["fade-out", 3],
]);
const NativeResizeType = new Map([
    ["auto", 0],
    ["stretch", 1],
    ["clip", 2],
    ["none", 3],
]);
const NativeAlignType = new Map([
    ["auto", 0],
    ["left-top", 1],
    ["left-center", 2],
    ["left-bottom", 3],
    ["right-top", 4],
    ["right-center", 5],
    ["right-bottom", 6],
    ["center-top", 7],
    ["center-center", 8],
    ["center-bottom", 9],
]);
const debugColors = {
    startNode: "#82B2E8",
    endNode: "#5EFF9B",
    pink: "#DC9CFF",
    startAncestor: "#E88F82",
    endAncestor: "#FFDC8F",
};
const debugStyles = StyleSheet.create({
    overlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: "black",
        opacity: 0.3,
    },
    text: {
        marginLeft: 3,
        marginTop: 3,
        fontSize: 10,
    },
    box: {
        position: "absolute",
        borderWidth: 1,
        borderStyle: "dashed",
    },
});
export const RNAnimatedSharedElementTransitionView = RNSharedElementTransitionView
    ? Animated.createAnimatedComponent(RNSharedElementTransitionView)
    : undefined;
export class SharedElementTransition extends React.Component {
    static prepareNode(node) {
        let nodeStyle = {};
        if (Platform.OS === "android" && node && node.parentInstance) {
            const child = React.Children.only(node.parentInstance.props.children);
            const props = child ? child.props : {};
            nodeStyle = StyleSheet.flatten([props.style]) || {};
            delete nodeStyle.transform;
            delete nodeStyle.opacity;
            nodeStyle.resizeMode = nodeStyle.resizeMode || props.resizeMode;
            if (nodeStyle.backgroundColor)
                nodeStyle.backgroundColor = processColor(nodeStyle.backgroundColor);
            if (nodeStyle.borderColor)
                nodeStyle.borderColor = processColor(nodeStyle.borderColor);
            if (nodeStyle.color)
                nodeStyle.color = processColor(nodeStyle.color);
        }
        return node
            ? {
                nodeHandle: node.nodeHandle,
                isParent: node.isParent,
                nodeStyle,
            }
            : undefined;
    }
    static defaultProps = {
        start: {},
        end: {},
        SharedElementComponent: RNAnimatedSharedElementTransitionView,
        animation: "move",
        resize: "auto",
        align: "auto",
    };
    constructor(props) {
        super(props);
        if (!props.SharedElementComponent &&
            !SharedElementTransition.isNotAvailableWarningShown) {
            SharedElementTransition.isNotAvailableWarningShown = true;
            if (Platform.OS === "android" || Platform.OS === "ios") {
                console.warn("RNSharedElementTransition is not available, did you forget to link `react-native-shared-element` into your project?");
            }
            else {
                console.warn("RNSharedElementTransition is not available on this platform");
            }
        }
    }
    state = {};
    static isNotAvailableWarningShown = false;
    onMeasureNode = (event) => {
        const { nativeEvent } = event;
        const { onMeasure } = this.props;
        this.setState({
            [`${nativeEvent.node}`]: nativeEvent,
        });
        // console.log("onMeasure: ", nativeEvent);
        if (onMeasure) {
            onMeasure(event);
        }
    };
    renderDebugOverlay() {
        if (!this.props.debug) {
            return;
        }
        return React.createElement(View, { style: debugStyles.overlay });
    }
    renderDebugLayer(name) {
        const event = this.state[name];
        if (!event || !this.props.debug)
            return;
        const { layout, style } = event;
        const isContentDifferent = layout.x !== layout.contentX ||
            layout.y !== layout.contentY ||
            layout.width !== layout.contentWidth ||
            layout.height !== layout.contentHeight;
        const isFullScreen = layout.visibleX === 0 &&
            layout.visibleY === 0 &&
            layout.visibleWidth === Dimensions.get("window").width &&
            layout.visibleHeight === Dimensions.get("window").height;
        const color = debugColors[name];
        return (React.createElement(View, { style: StyleSheet.absoluteFill },
            isContentDifferent ? (React.createElement(View, { style: [
                    debugStyles.box,
                    {
                        left: layout.contentX,
                        top: layout.contentY,
                        width: layout.contentWidth,
                        height: layout.contentHeight,
                        borderColor: color,
                        opacity: 0.5,
                    },
                ] },
                React.createElement(Text, { style: [debugStyles.text, { color }] }, "Content"))) : undefined,
            React.createElement(View, { style: [
                    debugStyles.box,
                    {
                        left: layout.x,
                        top: layout.y,
                        width: layout.width,
                        height: layout.height,
                        borderColor: color,
                        borderRadius: style.borderRadius || 0,
                    },
                ] },
                React.createElement(Text, { style: [
                        debugStyles.text,
                        { color, marginTop: Math.max((style.borderRadius || 0) - 7, 3) },
                    ] }, name)),
            React.createElement(View, { style: {
                    position: "absolute",
                    overflow: "hidden",
                    left: layout.visibleX,
                    top: layout.visibleY,
                    width: layout.visibleWidth,
                    height: layout.visibleHeight,
                } },
                React.createElement(View, { style: [
                        {
                            position: "absolute",
                            left: layout.x - layout.visibleX,
                            top: layout.y - layout.visibleY,
                            width: layout.width,
                            height: layout.height,
                            borderRadius: style.borderRadius || 0,
                            backgroundColor: isFullScreen ? "transparent" : color + "80",
                        },
                    ] }))));
    }
    render() {
        const { SharedElementComponent, start, end, position, animation, resize, align, onMeasure, debug, 
        // style,
        ...otherProps } = this.props;
        if (!SharedElementComponent) {
            return null;
        }
        return (React.createElement(View, { style: StyleSheet.absoluteFill },
            React.createElement(SharedElementComponent, { startNode: {
                    node: SharedElementTransition.prepareNode(start.node),
                    ancestor: SharedElementTransition.prepareNode(start.ancestor),
                }, endNode: {
                    node: SharedElementTransition.prepareNode(end.node),
                    ancestor: SharedElementTransition.prepareNode(end.ancestor),
                }, nodePosition: position, animation: NativeAnimationType.get(animation), 
                // @ts-ignore
                resize: NativeResizeType.get(resize), 
                // @ts-ignore
                align: NativeAlignType.get(align), onMeasureNode: debug ? this.onMeasureNode : onMeasure, ...otherProps }),
            this.renderDebugLayer("startNode"),
            this.renderDebugLayer("endNode")));
    }
}
//# sourceMappingURL=SharedElementTransition.js.map