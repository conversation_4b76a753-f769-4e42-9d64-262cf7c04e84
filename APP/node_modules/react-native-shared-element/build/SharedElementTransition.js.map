{"version": 3, "file": "SharedElementTransition.js", "sourceRoot": "", "sources": ["../src/SharedElementTransition.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EACL,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,UAAU,EACV,YAAY,EACZ,QAAQ,GACT,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC;AAuDhF,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAiC;IAClE,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,UAAU,EAAE,CAAC,CAAC;CAChB,CAAC,CAAC;AAEH,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAA8B;IAC5D,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,MAAM,EAAE,CAAC,CAAC;CACZ,CAAC,CAAC;AAEH,MAAM,eAAe,GAAG,IAAI,GAAG,CAA6B;IAC1D,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,aAAa,EAAE,CAAC,CAAC;IAClB,CAAC,aAAa,EAAE,CAAC,CAAC;IAClB,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,cAAc,EAAE,CAAC,CAAC;IACnB,CAAC,cAAc,EAAE,CAAC,CAAC;IACnB,CAAC,YAAY,EAAE,CAAC,CAAC;IACjB,CAAC,eAAe,EAAE,CAAC,CAAC;IACpB,CAAC,eAAe,EAAE,CAAC,CAAC;CACrB,CAAC,CAAC;AAEH,MAAM,WAAW,GAAG;IAClB,SAAS,EAAE,SAAS;IACpB,OAAO,EAAE,SAAS;IAClB,IAAI,EAAE,SAAS;IACf,aAAa,EAAE,SAAS;IACxB,WAAW,EAAE,SAAS;CACvB,CAAC;AAEF,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC;IACpC,OAAO,EAAE;QACP,GAAG,UAAU,CAAC,kBAAkB;QAChC,eAAe,EAAE,OAAO;QACxB,OAAO,EAAE,GAAG;KACb;IACD,IAAI,EAAE;QACJ,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,CAAC;QACZ,QAAQ,EAAE,EAAE;KACb;IACD,GAAG,EAAE;QACH,QAAQ,EAAE,UAAU;QACpB,WAAW,EAAE,CAAC;QACd,WAAW,EAAE,QAAQ;KACtB;CACF,CAAC,CAAC;AASH,MAAM,CAAC,MAAM,qCAAqC,GAChD,6BAA6B;IAC3B,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC,6BAA6B,CAAC;IACjE,CAAC,CAAC,SAAS,CAAC;AAEhB,MAAM,OAAO,uBAAwB,SAAQ,KAAK,CAAC,SAGlD;IACC,MAAM,CAAC,WAAW,CAAC,IAA8B;QAC/C,IAAI,SAAS,GAAQ,EAAE,CAAC;QACxB,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;YAC5D,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACtE,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACvC,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,SAAS,CAAC,SAAS,CAAC;YAC3B,OAAO,SAAS,CAAC,OAAO,CAAC;YACzB,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,UAAU,CAAC;YAChE,IAAI,SAAS,CAAC,eAAe;gBAC3B,SAAS,CAAC,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;YACtE,IAAI,SAAS,CAAC,WAAW;gBACvB,SAAS,CAAC,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YAC9D,IAAI,SAAS,CAAC,KAAK;gBAAE,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SACtE;QACD,OAAO,IAAI;YACT,CAAC,CAAC;gBACE,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,SAAS;aACV;YACH,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,YAAY,GAAG;QACpB,KAAK,EAAE,EAAE;QACT,GAAG,EAAE,EAAE;QACP,sBAAsB,EAAE,qCAAqC;QAC7D,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,KAAK,EAAE,MAAM;KACd,CAAC;IAEF,YAAY,KAAmC;QAC7C,KAAK,CAAC,KAAK,CAAC,CAAC;QACb,IACE,CAAC,KAAK,CAAC,sBAAsB;YAC7B,CAAC,uBAAuB,CAAC,0BAA0B,EACnD;YACA,uBAAuB,CAAC,0BAA0B,GAAG,IAAI,CAAC;YAC1D,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;gBACtD,OAAO,CAAC,IAAI,CACV,qHAAqH,CACtH,CAAC;aACH;iBAAM;gBACL,OAAO,CAAC,IAAI,CACV,6DAA6D,CAC9D,CAAC;aACH;SACF;IACH,CAAC;IAED,KAAK,GAAc,EAAE,CAAC;IAEd,MAAM,CAAC,0BAA0B,GAAG,KAAK,CAAC;IAElD,aAAa,GAAG,CAAC,KAAkC,EAAE,EAAE;QACrD,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;QAC9B,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC;YACZ,CAAC,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,WAAW;SACrC,CAAC,CAAC;QACH,2CAA2C;QAC3C,IAAI,SAAS,EAAE;YACb,SAAS,CAAC,KAAK,CAAC,CAAC;SAClB;IACH,CAAC,CAAC;IAEF,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YACrB,OAAO;SACR;QACD,OAAO,oBAAC,IAAI,IAAC,KAAK,EAAE,WAAW,CAAC,OAAO,GAAI,CAAC;IAC9C,CAAC;IAED,gBAAgB,CAAC,IAA2B;QAC1C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;YAAE,OAAO;QACxC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QAChC,MAAM,kBAAkB,GACtB,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,QAAQ;YAC5B,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC,QAAQ;YAC5B,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,YAAY;YACpC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,aAAa,CAAC;QACzC,MAAM,YAAY,GAChB,MAAM,CAAC,QAAQ,KAAK,CAAC;YACrB,MAAM,CAAC,QAAQ,KAAK,CAAC;YACrB,MAAM,CAAC,YAAY,KAAK,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,KAAK;YACtD,MAAM,CAAC,aAAa,KAAK,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QAE3D,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;QAChC,OAAO,CACL,oBAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,YAAY;YACjC,kBAAkB,CAAC,CAAC,CAAC,CACpB,oBAAC,IAAI,IACH,KAAK,EAAE;oBACL,WAAW,CAAC,GAAG;oBACf;wBACE,IAAI,EAAE,MAAM,CAAC,QAAQ;wBACrB,GAAG,EAAE,MAAM,CAAC,QAAQ;wBACpB,KAAK,EAAE,MAAM,CAAC,YAAY;wBAC1B,MAAM,EAAE,MAAM,CAAC,aAAa;wBAC5B,WAAW,EAAE,KAAK;wBAClB,OAAO,EAAE,GAAG;qBACb;iBACF;gBAED,oBAAC,IAAI,IAAC,KAAK,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,cAAgB,CACrD,CACR,CAAC,CAAC,CAAC,SAAS;YACb,oBAAC,IAAI,IACH,KAAK,EAAE;oBACL,WAAW,CAAC,GAAG;oBACf;wBACE,IAAI,EAAE,MAAM,CAAC,CAAC;wBACd,GAAG,EAAE,MAAM,CAAC,CAAC;wBACb,KAAK,EAAE,MAAM,CAAC,KAAK;wBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;wBACrB,WAAW,EAAE,KAAK;wBAClB,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;qBACtC;iBACF;gBAED,oBAAC,IAAI,IACH,KAAK,EAAE;wBACL,WAAW,CAAC,IAAI;wBAChB,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;qBACjE,IAEA,IAAI,CACA,CACF;YACP,oBAAC,IAAI,IACH,KAAK,EAAE;oBACL,QAAQ,EAAE,UAAU;oBACpB,QAAQ,EAAE,QAAQ;oBAClB,IAAI,EAAE,MAAM,CAAC,QAAQ;oBACrB,GAAG,EAAE,MAAM,CAAC,QAAQ;oBACpB,KAAK,EAAE,MAAM,CAAC,YAAY;oBAC1B,MAAM,EAAE,MAAM,CAAC,aAAa;iBAC7B;gBAED,oBAAC,IAAI,IACH,KAAK,EAAE;wBACL;4BACE,QAAQ,EAAE,UAAU;4BACpB,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ;4BAChC,GAAG,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ;4BAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;4BACnB,MAAM,EAAE,MAAM,CAAC,MAAM;4BACrB,YAAY,EAAE,KAAK,CAAC,YAAY,IAAI,CAAC;4BACrC,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI;yBAC7D;qBACF,GACD,CACG,CACF,CACR,CAAC;IACJ,CAAC;IAED,MAAM;QACJ,MAAM,EACJ,sBAAsB,EACtB,KAAK,EACL,GAAG,EACH,QAAQ,EACR,SAAS,EACT,MAAM,EACN,KAAK,EACL,SAAS,EACT,KAAK;QACL,SAAS;QACT,GAAG,UAAU,EACd,GAAG,IAAI,CAAC,KAAK,CAAC;QACf,IAAI,CAAC,sBAAsB,EAAE;YAC3B,OAAO,IAAI,CAAC;SACb;QACD,OAAO,CACL,oBAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,YAAY;YAClC,oBAAC,sBAAsB,IACrB,SAAS,EAAE;oBACT,IAAI,EAAE,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;oBACrD,QAAQ,EAAE,uBAAuB,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC;iBAC9D,EACD,OAAO,EAAE;oBACP,IAAI,EAAE,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;oBACnD,QAAQ,EAAE,uBAAuB,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC;iBAC5D,EACD,YAAY,EAAE,QAAQ,EACtB,SAAS,EAAE,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC;gBAC7C,aAAa;gBACb,MAAM,EAAE,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC;gBACpC,aAAa;gBACb,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,EACjC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,KAEjD,UAAU,GACd;YAED,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;YAClC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAC5B,CACR,CAAC;IACJ,CAAC", "sourcesContent": ["import * as React from \"react\";\nimport {\n  View,\n  Text,\n  Animated,\n  Dimensions,\n  StyleSheet,\n  processColor,\n  Platform,\n} from \"react-native\";\n\nimport { RNSharedElementTransitionView } from \"./RNSharedElementTransitionView\";\nimport {\n  SharedElementNode,\n  SharedElementAnimation,\n  SharedElementResize,\n  SharedElementAlign,\n  SharedElementNodeType,\n  SharedElementContentType,\n} from \"./types\";\n\nexport type SharedElementMeasureData = {\n  node: SharedElementNodeType;\n  layout: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n    visibleX: number;\n    visibleY: number;\n    visibleWidth: number;\n    visibleHeight: number;\n    contentX: number;\n    contentY: number;\n    contentWidth: number;\n    contentHeight: number;\n  };\n  contentType: SharedElementContentType;\n  style: {\n    borderRadius: number;\n  };\n};\n\nexport type SharedElementOnMeasureEvent = {\n  nativeEvent: SharedElementMeasureData;\n};\n\nexport type SharedElementTransitionProps = {\n  start: {\n    node: SharedElementNode | null;\n    ancestor: SharedElementNode | null;\n  };\n  end: {\n    node: SharedElementNode | null;\n    ancestor: SharedElementNode | null;\n  };\n  position: number | any | void;\n  animation: SharedElementAnimation;\n  resize?: SharedElementResize;\n  align?: SharedElementAlign;\n  debug?: boolean;\n  style?: any;\n  onMeasure?: (event: SharedElementOnMeasureEvent) => void;\n  SharedElementComponent?: any;\n};\n\nconst NativeAnimationType = new Map<SharedElementAnimation, number>([\n  [\"move\", 0],\n  [\"fade\", 1],\n  [\"fade-in\", 2],\n  [\"fade-out\", 3],\n]);\n\nconst NativeResizeType = new Map<SharedElementResize, number>([\n  [\"auto\", 0],\n  [\"stretch\", 1],\n  [\"clip\", 2],\n  [\"none\", 3],\n]);\n\nconst NativeAlignType = new Map<SharedElementAlign, number>([\n  [\"auto\", 0],\n  [\"left-top\", 1],\n  [\"left-center\", 2],\n  [\"left-bottom\", 3],\n  [\"right-top\", 4],\n  [\"right-center\", 5],\n  [\"right-bottom\", 6],\n  [\"center-top\", 7],\n  [\"center-center\", 8],\n  [\"center-bottom\", 9],\n]);\n\nconst debugColors = {\n  startNode: \"#82B2E8\",\n  endNode: \"#5EFF9B\",\n  pink: \"#DC9CFF\",\n  startAncestor: \"#E88F82\",\n  endAncestor: \"#FFDC8F\",\n};\n\nconst debugStyles = StyleSheet.create({\n  overlay: {\n    ...StyleSheet.absoluteFillObject,\n    backgroundColor: \"black\",\n    opacity: 0.3,\n  },\n  text: {\n    marginLeft: 3,\n    marginTop: 3,\n    fontSize: 10,\n  },\n  box: {\n    position: \"absolute\",\n    borderWidth: 1,\n    borderStyle: \"dashed\",\n  },\n});\n\ntype StateType = {\n  startNode?: SharedElementMeasureData;\n  endNode?: SharedElementMeasureData;\n  startAncestor?: SharedElementMeasureData;\n  endAncestor?: SharedElementMeasureData;\n};\n\nexport const RNAnimatedSharedElementTransitionView =\n  RNSharedElementTransitionView\n    ? Animated.createAnimatedComponent(RNSharedElementTransitionView)\n    : undefined;\n\nexport class SharedElementTransition extends React.Component<\n  SharedElementTransitionProps,\n  StateType\n> {\n  static prepareNode(node: SharedElementNode | null): any {\n    let nodeStyle: any = {};\n    if (Platform.OS === \"android\" && node && node.parentInstance) {\n      const child = React.Children.only(node.parentInstance.props.children);\n      const props = child ? child.props : {};\n      nodeStyle = StyleSheet.flatten([props.style]) || {};\n      delete nodeStyle.transform;\n      delete nodeStyle.opacity;\n      nodeStyle.resizeMode = nodeStyle.resizeMode || props.resizeMode;\n      if (nodeStyle.backgroundColor)\n        nodeStyle.backgroundColor = processColor(nodeStyle.backgroundColor);\n      if (nodeStyle.borderColor)\n        nodeStyle.borderColor = processColor(nodeStyle.borderColor);\n      if (nodeStyle.color) nodeStyle.color = processColor(nodeStyle.color);\n    }\n    return node\n      ? {\n          nodeHandle: node.nodeHandle,\n          isParent: node.isParent,\n          nodeStyle,\n        }\n      : undefined;\n  }\n\n  static defaultProps = {\n    start: {},\n    end: {},\n    SharedElementComponent: RNAnimatedSharedElementTransitionView,\n    animation: \"move\",\n    resize: \"auto\",\n    align: \"auto\",\n  };\n\n  constructor(props: SharedElementTransitionProps) {\n    super(props);\n    if (\n      !props.SharedElementComponent &&\n      !SharedElementTransition.isNotAvailableWarningShown\n    ) {\n      SharedElementTransition.isNotAvailableWarningShown = true;\n      if (Platform.OS === \"android\" || Platform.OS === \"ios\") {\n        console.warn(\n          \"RNSharedElementTransition is not available, did you forget to link `react-native-shared-element` into your project?\"\n        );\n      } else {\n        console.warn(\n          \"RNSharedElementTransition is not available on this platform\"\n        );\n      }\n    }\n  }\n\n  state: StateType = {};\n\n  private static isNotAvailableWarningShown = false;\n\n  onMeasureNode = (event: SharedElementOnMeasureEvent) => {\n    const { nativeEvent } = event;\n    const { onMeasure } = this.props;\n    this.setState({\n      [`${nativeEvent.node}`]: nativeEvent,\n    });\n    // console.log(\"onMeasure: \", nativeEvent);\n    if (onMeasure) {\n      onMeasure(event);\n    }\n  };\n\n  renderDebugOverlay() {\n    if (!this.props.debug) {\n      return;\n    }\n    return <View style={debugStyles.overlay} />;\n  }\n\n  renderDebugLayer(name: SharedElementNodeType) {\n    const event = this.state[name];\n    if (!event || !this.props.debug) return;\n    const { layout, style } = event;\n    const isContentDifferent =\n      layout.x !== layout.contentX ||\n      layout.y !== layout.contentY ||\n      layout.width !== layout.contentWidth ||\n      layout.height !== layout.contentHeight;\n    const isFullScreen =\n      layout.visibleX === 0 &&\n      layout.visibleY === 0 &&\n      layout.visibleWidth === Dimensions.get(\"window\").width &&\n      layout.visibleHeight === Dimensions.get(\"window\").height;\n\n    const color = debugColors[name];\n    return (\n      <View style={StyleSheet.absoluteFill}>\n        {isContentDifferent ? (\n          <View\n            style={[\n              debugStyles.box,\n              {\n                left: layout.contentX,\n                top: layout.contentY,\n                width: layout.contentWidth,\n                height: layout.contentHeight,\n                borderColor: color,\n                opacity: 0.5,\n              },\n            ]}\n          >\n            <Text style={[debugStyles.text, { color }]}>Content</Text>\n          </View>\n        ) : undefined}\n        <View\n          style={[\n            debugStyles.box,\n            {\n              left: layout.x,\n              top: layout.y,\n              width: layout.width,\n              height: layout.height,\n              borderColor: color,\n              borderRadius: style.borderRadius || 0,\n            },\n          ]}\n        >\n          <Text\n            style={[\n              debugStyles.text,\n              { color, marginTop: Math.max((style.borderRadius || 0) - 7, 3) },\n            ]}\n          >\n            {name}\n          </Text>\n        </View>\n        <View\n          style={{\n            position: \"absolute\",\n            overflow: \"hidden\",\n            left: layout.visibleX,\n            top: layout.visibleY,\n            width: layout.visibleWidth,\n            height: layout.visibleHeight,\n          }}\n        >\n          <View\n            style={[\n              {\n                position: \"absolute\",\n                left: layout.x - layout.visibleX,\n                top: layout.y - layout.visibleY,\n                width: layout.width,\n                height: layout.height,\n                borderRadius: style.borderRadius || 0,\n                backgroundColor: isFullScreen ? \"transparent\" : color + \"80\",\n              },\n            ]}\n          />\n        </View>\n      </View>\n    );\n  }\n\n  render() {\n    const {\n      SharedElementComponent,\n      start,\n      end,\n      position,\n      animation,\n      resize,\n      align,\n      onMeasure,\n      debug,\n      // style,\n      ...otherProps\n    } = this.props;\n    if (!SharedElementComponent) {\n      return null;\n    }\n    return (\n      <View style={StyleSheet.absoluteFill}>\n        <SharedElementComponent\n          startNode={{\n            node: SharedElementTransition.prepareNode(start.node),\n            ancestor: SharedElementTransition.prepareNode(start.ancestor),\n          }}\n          endNode={{\n            node: SharedElementTransition.prepareNode(end.node),\n            ancestor: SharedElementTransition.prepareNode(end.ancestor),\n          }}\n          nodePosition={position}\n          animation={NativeAnimationType.get(animation)}\n          // @ts-ignore\n          resize={NativeResizeType.get(resize)}\n          // @ts-ignore\n          align={NativeAlignType.get(align)}\n          onMeasureNode={debug ? this.onMeasureNode : onMeasure}\n          // style={debug && style ? [debugStyles.content, style] : style}\n          {...otherProps}\n        />\n        {/*this.renderDebugOverlay()*/}\n        {this.renderDebugLayer(\"startNode\")}\n        {this.renderDebugLayer(\"endNode\")}\n      </View>\n    );\n  }\n}\n"]}