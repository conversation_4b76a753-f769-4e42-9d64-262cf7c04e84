{"version": 3, "file": "RNSharedElementTransitionView.js", "sourceRoot": "", "sources": ["../src/RNSharedElementTransitionView.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAErE,MAAM,WAAW,GAAG,CAAC,CAAC,aAAa,CAAC,yBAAyB,CAAC;AAE9D,IAAI,WAAW,EAAE;IACf,aAAa,CAAC,yBAAyB,CAAC,SAAS,CAAC;QAChD,cAAc,EAAE;YACd,qCAAqC,EAAE,0BAA0B;SAClE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACjC,CAAC,CAAC;CACJ;AAED,MAAM,CAAC,MAAM,6BAA6B,GAAG,WAAW;IACtD,CAAC,CAAC,sBAAsB,CAAC,2BAA2B,CAAC;IACrD,CAAC,CAAC,SAAS,CAAC", "sourcesContent": ["import { requireNativeComponent, NativeModules } from \"react-native\";\n\nconst isAvailable = !!NativeModules.RNSharedElementTransition;\n\nif (isAvailable) {\n  NativeModules.RNSharedElementTransition.configure({\n    imageResolvers: [\n      \"RNPhotoView.MWTapDetectingImageView\", // react-native-photo-view\n    ].map((path) => path.split(\".\")),\n  });\n}\n\nexport const RNSharedElementTransitionView = isAvailable\n  ? requireNativeComponent(\"RNSharedElementTransition\")\n  : undefined;\n"]}