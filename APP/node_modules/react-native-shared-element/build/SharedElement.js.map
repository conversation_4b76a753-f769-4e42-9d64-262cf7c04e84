{"version": 3, "file": "SharedElement.js", "sourceRoot": "", "sources": ["../src/SharedElement.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,IAAI,EAAE,cAAc,EAAa,MAAM,cAAc,CAAC;AAS/D,MAAM,UAAU,WAAW,CACzB,GAAQ,EACR,QAAkB,EAClB,cAAoB;IAEpB,MAAM,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACzD,OAAO,UAAU;QACf,CAAC,CAAC;YACE,GAAG;YACH,UAAU;YACV,QAAQ,EAAE,QAAQ,IAAI,KAAK;YAC3B,cAAc;SACf;QACH,CAAC,CAAC,IAAI,CAAC;AACX,CAAC;AAED,MAAM,OAAO,aAAc,SAAQ,KAAK,CAAC,SAA6B;IACpE,kBAAkB,CAAC,SAA6B;QAC9C,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,EAAE;YACxD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC;IAEO,KAAK,GAA6B,IAAI,CAAC;IAEvC,QAAQ,GAAG,CAAC,GAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC,CAAC;IAEF,MAAM;QACJ,MAAM,EACJ,MAAM,EAAE,uDAAuD;QAC/D,GAAG,UAAU,EACd,GAAG,IAAI,CAAC,KAAK,CAAC;QACf,OAAO,oBAAC,IAAI,IAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,KAAM,UAAU,GAAI,CAAC;IAC1E,CAAC;CACF", "sourcesContent": ["import * as React from \"react\";\nimport { View, findNode<PERSON>and<PERSON>, ViewProps } from \"react-native\";\n\nimport { SharedElementNode } from \"./types\";\n\nexport type SharedElementProps = ViewProps & {\n  children: React.ReactNode;\n  onNode: (node: SharedElementNode | null) => void;\n};\n\nexport function nodeFromRef(\n  ref: any,\n  isParent?: boolean,\n  parentInstance?: any\n): SharedElementNode | null {\n  const nodeHandle = ref ? findNodeHandle(ref) : undefined;\n  return nodeHandle\n    ? {\n        ref,\n        nodeHandle,\n        isParent: isParent || false,\n        parentInstance,\n      }\n    : null;\n}\n\nexport class SharedElement extends React.Component<SharedElementProps> {\n  componentDidUpdate(prevProps: SharedElementProps) {\n    if (!prevProps.onNode && this.props.onNode && this._node) {\n      this.props.onNode(this._node);\n    }\n  }\n\n  private _node: SharedElementNode | null = null;\n\n  private onSetRef = (ref: any) => {\n    this._node = nodeFromRef(ref, true, this);\n    if (this.props.onNode) {\n      this.props.onNode(this._node);\n    }\n  };\n\n  render() {\n    const {\n      onNode, //eslint-disable-line @typescript-eslint/no-unused-vars\n      ...otherProps\n    } = this.props;\n    return <View ref={this.onSetRef} collapsable={false} {...otherProps} />;\n  }\n}\n"]}