{"version": 3, "file": "RNSharedElementNodeManager.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementNodeManager.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAGhE,MAAM,OAAO,0BAA0B;IAC7B,KAAK,GAAG,IAAI,GAAG,EAAoC,CAAC;IACpD,MAAM,CAAC,QAAQ,CAA6B;IAEpD,MAAM,CAAC,WAAW;QAChB,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE;YACxC,0BAA0B,CAAC,QAAQ,GAAG,IAAI,0BAA0B,EAAE,CAAC;SACxE;QACD,OAAO,0BAA0B,CAAC,QAAQ,CAAC;IAC7C,CAAC;IAED,OAAO,CACL,OAAqB,EACrB,QAAiB,EACjB,eAA6B;QAE7B,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,IAAI,EAAE;YACR,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;SACb;QACD,IAAI,GAAG,IAAI,mBAAmB,CAAC,OAAO,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,IAAyB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,QAAQ,EAAE;YACb,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACjC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF", "sourcesContent": ["import { RNSharedElementNode } from \"./RNSharedElementNode.web\";\nimport { IHTMLElement } from \"./types\";\n\nexport class RNSharedElementNodeManager {\n  private nodes = new Map<HTMLElement, RNSharedElementNode>();\n  private static instance: RNSharedElementNodeManager;\n\n  static getInstance(): RNSharedElementNodeManager {\n    if (!RNSharedElementNodeManager.instance) {\n      RNSharedElementNodeManager.instance = new RNSharedElementNodeManager();\n    }\n    return RNSharedElementNodeManager.instance;\n  }\n\n  acquire(\n    domNode: IHTMLElement,\n    isParent: boolean,\n    ancestorDomNode: IHTMLElement\n  ): RNSharedElementNode {\n    let node = this.nodes.get(domNode);\n    if (node) {\n      node.addRef();\n      return node;\n    }\n    node = new RNSharedElementNode(domNode, isParent, ancestorDomNode);\n    this.nodes.set(domNode, node);\n    return node;\n  }\n\n  release(node: RNSharedElementNode) {\n    const refCount = node.releaseRef();\n    if (!refCount) {\n      this.nodes.delete(node.domNode);\n    }\n    return refCount;\n  }\n}\n"]}