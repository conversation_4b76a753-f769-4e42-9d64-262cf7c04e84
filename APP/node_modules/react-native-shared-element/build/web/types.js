export var RNSharedElementAnimation;
(function (RNSharedElementAnimation) {
    RNSharedElementAnimation[RNSharedElementAnimation["Move"] = 0] = "Move";
    RNSharedElementAnimation[RNSharedElementAnimation["Fade"] = 1] = "Fade";
    RNSharedElementAnimation[RNSharedElementAnimation["FadeIn"] = 2] = "FadeIn";
    RNSharedElementAnimation[RNSharedElementAnimation["FadeOut"] = 3] = "FadeOut";
})(RNSharedElementAnimation || (RNSharedElementAnimation = {}));
export var RNSharedElementResize;
(function (RNSharedElementResize) {
    RNSharedElementResize[RNSharedElementResize["Auto"] = 0] = "Auto";
    RNSharedElementResize[RNSharedElementResize["Stretch"] = 1] = "Stretch";
    RNSharedElementResize[RNSharedElementResize["Clip"] = 2] = "Clip";
    RNSharedElementResize[RNSharedElementResize["None"] = 3] = "None";
})(RNSharedElementResize || (RNSharedElementResize = {}));
export var RNSharedElementAlign;
(function (RNSharedElementAlign) {
    RNSharedElementAlign[RNSharedElementAlign["Auto"] = 0] = "Auto";
    RNSharedElementAlign[RNSharedElementAlign["LeftTop"] = 1] = "LeftTop";
    RNSharedElementAlign[RNSharedElementAlign["LeftCenter"] = 2] = "LeftCenter";
    RNSharedElementAlign[RNSharedElementAlign["LeftBottom"] = 3] = "LeftBottom";
    RNSharedElementAlign[RNSharedElementAlign["RightTop"] = 4] = "RightTop";
    RNSharedElementAlign[RNSharedElementAlign["RightCenter"] = 5] = "RightCenter";
    RNSharedElementAlign[RNSharedElementAlign["RightBottom"] = 6] = "RightBottom";
    RNSharedElementAlign[RNSharedElementAlign["CenterTop"] = 7] = "CenterTop";
    RNSharedElementAlign[RNSharedElementAlign["CenterCenter"] = 8] = "CenterCenter";
    RNSharedElementAlign[RNSharedElementAlign["CenterBottom"] = 9] = "CenterBottom";
})(RNSharedElementAlign || (RNSharedElementAlign = {}));
//# sourceMappingURL=types.js.map