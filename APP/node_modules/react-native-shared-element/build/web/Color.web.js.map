{"version": 3, "file": "Color.web.js", "sourceRoot": "", "sources": ["../../src/web/Color.web.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,UAAU,CAAC,KAAqB;IAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IACvC,IAAI,KAAK,CAAC;IACV,MAAM,CAAC,GAAG,QAAQ,CAAC;IACnB,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,oBAAoB;IAEtD,wDAAwD;IACxD,IAAI,CAAC,KAAK,GAAG,gDAAgD,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxE,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAChE,wDAAwD;SACnD,IAAI,CAAC,KAAK,GAAG,uCAAuC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpE,OAAO;YACL,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;YACpB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;YACpB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;YACpB,CAAC;SACF,CAAC;IACJ,yCAAyC;IACzC,qDAAqD;SAChD,IACH,CAAC,KAAK,GAAG,qDAAqD,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE3E,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACtD,wCAAwC;IACxC,qDAAqD;SAChD,IAAI,CAAC,KAAK,GAAG,gCAAgC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7D,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C,wDAAwD;;QACnD,MAAM,IAAI,KAAK,CAAC,KAAK,GAAG,iCAAiC,CAAC,CAAC;AAClE,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,KAAY;IACtC,OAAO,QAAQ,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,MAAa,EACb,MAAa,EACb,QAAgB;IAEhB,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC;AAC9D,CAAC", "sourcesContent": ["export type Color = number[];\n\nexport function parseColor(color: string | Color): Color {\n  if (Array.isArray(color)) return color;\n  let cache;\n  const p = parseInt;\n  color = color.replace(/\\s/g, \"\"); // Remove all spaces\n\n  // Checks for 6 digit hex and converts string to integer\n  if ((cache = /#([\\da-fA-F]{2})([\\da-fA-F]{2})([\\da-fA-F]{2})/.exec(color)))\n    return [p(cache[1], 16), p(cache[2], 16), p(cache[3], 16), 1];\n  // Checks for 3 digit hex and converts string to integer\n  else if ((cache = /#([\\da-fA-F])([\\da-fA-F])([\\da-fA-F])/.exec(color)))\n    return [\n      p(cache[1], 16) * 17,\n      p(cache[2], 16) * 17,\n      p(cache[3], 16) * 17,\n      1,\n    ];\n  // Checks for rgba and converts string to\n  // integer/float using unary + operator to save bytes\n  else if (\n    (cache = /rgba\\(([\\d]+),([\\d]+),([\\d]+),([\\d]+|[\\d]*.[\\d]+)\\)/.exec(color))\n  )\n    return [+cache[1], +cache[2], +cache[3], +cache[4]];\n  // Checks for rgb and converts string to\n  // integer/float using unary + operator to save bytes\n  else if ((cache = /rgb\\(([\\d]+),([\\d]+),([\\d]+)\\)/.exec(color)))\n    return [+cache[1], +cache[2], +cache[3], 1];\n  // Otherwise throw an exception to make debugging easier\n  else throw new Error(color + \" is not supported by parseColor\");\n}\n\nexport function formatColor(color: Color): string {\n  return `rgba(${color[0]},${color[1]},${color[2]},${color[3]})`;\n}\n\nexport function interpolateColor(\n  color1: Color,\n  color2: Color,\n  position: number\n): Color {\n  return color1.map((c, i) => c + (color2[i] - c) * position);\n}\n"]}