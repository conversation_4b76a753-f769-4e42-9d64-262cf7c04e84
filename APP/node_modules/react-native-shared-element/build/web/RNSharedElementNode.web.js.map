{"version": 3, "file": "RNSharedElementNode.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementNode.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AACtE,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAUlC,MAAM,OAAO,mBAAmB;IACd,OAAO,CAAe;IACtB,eAAe,CAAe;IAC9B,QAAQ,CAAU;IAC1B,YAAY,GAAW,CAAC,CAAC;IACzB,WAAW,GAAkB,IAAI,CAAC;IAClC,QAAQ,GAAW,CAAC,CAAC;IACrB,UAAU,GAAgC,IAAI,CAAC;IAC/C,cAAc,GAA8C,IAAI,CAAC;IACjE,YAAY,GAAkC,IAAI,CAAC;IACnD,gBAAgB,GAAgD,IAAI,CAAC;IAE7E,YACE,OAAqB,EACrB,QAAiB,EACjB,eAA6B;QAE7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;IACzC,CAAC;IAED,MAAM;QACJ,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,UAAU;QACR,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,UAAU;QACR,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YACrC,IAAI,CAAC,WAAW,GAAG,OAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;YAC1C,OAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;SAC9B;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YAC3B,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;YACrC,aAAa;YACb,OAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;SAC3C;IACH,CAAC;IAED,IAAI,eAAe;QACjB,IAAI,OAAO,GAAiB,IAAI,CAAC,OAAO,CAAC;QAEzC,qDAAqD;QACrD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBACnC,aAAa;gBACb,OAAO,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aACjC;iBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE;gBACzC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO,IAAI,CAAC;aACb;SACF;QAED,4BAA4B;QAC5B,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;QAC/B,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1B,aAAa;gBACb,MAAM,SAAS,GAAiB,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,SAAS,CAAC,OAAO,KAAK,KAAK,EAAE;oBAC/B,aAAa;oBACb,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACpC,MAAM;iBACP;aACF;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,YAAY;QACV,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACzC;QACD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,EAAE,CAAC;YAChD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC7B,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBACvC,mBAAmB;aACpB;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB;QACvB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACvC,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ;YAAE,OAAO,KAAK,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,cAAc;YAAE,OAAO,IAAI,CAAC;QAEtC,eAAe;QACf,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;QAC7C,sDAAsD;QACtD,MAAM,YAAY,GAAG,QAAQ,CAAC,qBAAqB,EAAE,CAAC;QACtD,oEAAoE;QACpE,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO;QAC1C,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO;QAC1C,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC;YACtB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,UAAU;YACtB,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,UAAU;YACtB,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC,CAAC;QAEH,eAAe;QACf,MAAM,KAAK,GAAG,IAAI,oBAAoB,CACpC,MAAM;QACN,aAAa;QACb,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CACvC,CAAC;QAEF,2CAA2C;QAE3C,eAAe;QACf,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,mBAAmB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAC3B,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,IAAI,CAAC,YAAY;YAAE,OAAO,IAAI,CAAC,YAAY,CAAC;QAEhD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,gBAAgB;gBAAE,OAAO;YAClC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,IAAI,EAAE,CAAC;YACpD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,iCAAiC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,mBAAmB;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC;QACrC,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,gBAAgB;YAAE,OAAO,IAAI,CAAC;QAExC,qBAAqB;QACrB,MAAM,IAAI,GAAG,MAAM,sBAAsB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC3D,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,KAAK,CAAC;SACd;QAED,iBAAiB;QACjB,MAAM,OAAO,GAAG,IAAI,sBAAsB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAE1D,+CAA+C;QAE/C,eAAe;QACf,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAE5B,mBAAmB;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "sourcesContent": ["import { RNSharedElementContent } from \"./RNSharedElementContent.web\";\nimport { RNSharedElementStyle } from \"./RNSharedElementStyle.web\";\nimport { Rect } from \"./Rect.web\";\nimport { IHTMLElement } from \"./types\";\n\nexport type RNSharedElementNodeStyleCallback = (\n  value: RNSharedElementStyle\n) => void;\nexport type RNSharedElementNodeContentCallback = (\n  value: RNSharedElementContent\n) => void;\n\nexport class RNSharedElementNode {\n  public readonly domNode: IHTMLElement;\n  public readonly ancestorDomNode: IHTMLElement;\n  public readonly isParent: boolean;\n  private hideRefCount: number = 0;\n  private hideOpacity: string | null = null;\n  private refCount: number = 1;\n  private styleCache: RNSharedElementStyle | null = null;\n  private styleCallbacks: RNSharedElementNodeStyleCallback[] | null = null;\n  private contentCache: RNSharedElementContent | null = null;\n  private contentCallbacks: RNSharedElementNodeContentCallback[] | null = null;\n\n  constructor(\n    domNode: IHTMLElement,\n    isParent: boolean,\n    ancestorDomNode: IHTMLElement\n  ) {\n    this.domNode = domNode;\n    this.isParent = isParent;\n    this.ancestorDomNode = ancestorDomNode;\n  }\n\n  addRef() {\n    return ++this.refCount;\n  }\n\n  releaseRef() {\n    return --this.refCount;\n  }\n\n  addHideRef() {\n    this.hideRefCount++;\n    if (this.hideRefCount === 1) {\n      const element = this.resolvedElement;\n      this.hideOpacity = element!.style.opacity;\n      element!.style.opacity = \"0\";\n    }\n  }\n\n  releaseHideRef() {\n    this.hideRefCount--;\n    if (this.hideRefCount === 0) {\n      const element = this.resolvedElement;\n      // @ts-ignore\n      element!.style.opacity = this.hideOpacity;\n    }\n  }\n\n  get resolvedElement(): IHTMLElement | null {\n    let element: IHTMLElement = this.domNode;\n\n    // If this node is a parent, look for the first child\n    if (this.isParent) {\n      if (element.childNodes.length === 1) {\n        // @ts-ignore\n        element = element.childNodes[0];\n      } else if (element.childNodes.length <= 0) {\n        console.log(\"Child for parent doesnt exist\");\n        return null;\n      }\n    }\n\n    // Get background-image node\n    const { childNodes } = element;\n    if (childNodes.length === 2) {\n      for (let i = 0; i < 2; i++) {\n        // @ts-ignore\n        const childNode: IHTMLElement = childNodes[i];\n        if (childNode.tagName === \"IMG\") {\n          // @ts-ignore\n          element = childNodes[i ? 0 : i + 1];\n          break;\n        }\n      }\n    }\n\n    return element;\n  }\n\n  get resolvedAncestor(): IHTMLElement | null {\n    return this.ancestorDomNode;\n  }\n\n  requestStyle(): Promise<RNSharedElementStyle> {\n    if (this.styleCache) {\n      return Promise.resolve(this.styleCache);\n    }\n    return new Promise((resolve) => {\n      this.styleCallbacks = this.styleCallbacks || [];\n      this.styleCallbacks.push(resolve);\n      if (!this.fetchInitialStyle()) {\n        console.debug(\"Failed to fetch style\");\n        //startRetryLoop();\n      }\n    });\n  }\n\n  private fetchInitialStyle(): boolean {\n    const element = this.resolvedElement;\n    const ancestor = this.resolvedAncestor;\n    if (!element || !ancestor) return false;\n    if (!this.styleCallbacks) return true;\n\n    // Fetch layout\n    const rect = element.getBoundingClientRect();\n    // const ancestorTransform = ancestor.style.transform;\n    const ancestorRect = ancestor.getBoundingClientRect();\n    // console.log(\"ancestorTransform: \", ancestor.style, ancestorRect);\n    const translateX = ancestorRect.x; // TODO\n    const translateY = ancestorRect.y; // TODO\n    const layout = new Rect({\n      x: rect.x - translateX,\n      y: rect.y - translateY,\n      width: rect.width,\n      height: rect.height,\n    });\n\n    // Create style\n    const style = new RNSharedElementStyle(\n      layout,\n      // @ts-ignore\n      window.getComputedStyle(element, null)\n    );\n\n    // console.debug(\"Style fetched: \", style);\n\n    // Update cache\n    this.styleCache = style;\n\n    // Notify callbacks\n    const callbacks = this.styleCallbacks;\n    this.styleCallbacks = null;\n    callbacks.forEach((callback) => callback(style));\n    return true;\n  }\n\n  async requestContent(): Promise<RNSharedElementContent> {\n    if (this.contentCache) return this.contentCache;\n\n    return new Promise((resolve) => {\n      if (this.contentCallbacks) return;\n      this.contentCallbacks = this.contentCallbacks || [];\n      this.contentCallbacks.push(resolve);\n      this.fetchInitialContent();\n      // TODO RETRY IN CASE OF FAILURE?\n    });\n  }\n\n  private async fetchInitialContent(): Promise<boolean> {\n    const element = this.resolvedElement;\n    if (!element) return false;\n    if (!this.contentCallbacks) return true;\n\n    // Fetch content size\n    const size = await RNSharedElementContent.getSize(element);\n    if (!size) {\n      return false;\n    }\n\n    // Create content\n    const content = new RNSharedElementContent(element, size);\n\n    // console.debug(\"Content fetched: \", content);\n\n    // Update cache\n    this.contentCache = content;\n\n    // Notify callbacks\n    const callbacks = this.contentCallbacks;\n    this.contentCallbacks = null;\n    callbacks.forEach((callback) => callback(content));\n    return true;\n  }\n}\n"]}