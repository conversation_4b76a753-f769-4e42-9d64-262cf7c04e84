export class Rect {
    x = 0;
    y = 0;
    width = 0;
    height = 0;
    static empty = new Rect();
    constructor(source) {
        if (source) {
            this.x = source.x;
            this.y = source.y;
            this.width = source.width;
            this.height = source.height;
        }
    }
    equals(rect) {
        return (this.x === rect.x &&
            this.y === rect.y &&
            this.width === rect.width &&
            this.height === rect.height);
    }
}
//# sourceMappingURL=Rect.web.js.map