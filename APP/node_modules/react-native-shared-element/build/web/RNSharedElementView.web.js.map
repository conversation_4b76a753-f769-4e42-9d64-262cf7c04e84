{"version": 3, "file": "RNSharedElementView.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementView.web.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAEL,oBAAoB,EACpB,qBAAqB,GACtB,MAAM,SAAS,CAAC;AAEjB,SAAS,WAAW,CAAC,OAAqB;IACxC,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;IAE1B,gCAAgC;IAChC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;IAC5B,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;IACnB,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC;IAClB,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC;IAC7B,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC;IAChC,KAAK,CAAC,cAAc,GAAG,aAAa,CAAC;IACrC,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC;IACpC,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC;IACnC,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC;IAC/B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAE1B,eAAe;IACf,gCAAgC;IAEhC,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,eAAe,CACtB,aAAqB,CAAC,EACtB,aAAqB,CAAC,EACtB,SAAiB,CAAC,EAClB,SAAiB,CAAC;IAElB,OAAO;MACH,MAAM;SACH,MAAM;;MAET,UAAU,KAAK,UAAU;EAC7B,CAAC;AACH,CAAC;AAED,MAAM,OAAO,mBAAmB;IACvB,YAAY,GAAS,IAAI,CAAC,KAAK,CAAC;IACvB,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9D,MAAM,GAAS,IAAI,CAAC,KAAK,CAAC;IAC1B,KAAK,GAAgC,IAAI,CAAC;IAC1C,cAAc,GAAS,IAAI,CAAC,KAAK,CAAC;IAClC,eAAe,GAAwB,IAAI,CAAC;IAC5C,aAAa,GAAS,IAAI,CAAC,KAAK,CAAC;IACjC,MAAM,GAA0B,qBAAqB,CAAC,IAAI,CAAC;IAC3D,KAAK,GAAyB,oBAAoB,CAAC,IAAI,CAAC;IAE/D,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IACD,IAAI,cAAc,CAAC,KAA0B;QAC3C,IAAI,IAAI,CAAC,eAAe,KAAK,KAAK;YAAE,OAAO;QAC3C,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzD,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAChD;IACH,CAAC;IAEM,YAAY;QACjB,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAC/C,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO;QAEtC,qDAAqD;QACrD,IACE,MAAM,KAAK,qBAAqB,CAAC,OAAO;YACxC,CAAC,MAAM,KAAK,qBAAqB,CAAC,IAAI,IAAI,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EACvE;YACA,IAAI,CAAC,oBAAoB,EAAE,CAAC;SAC7B;aAAM;YACL,IAAI,CAAC,qBAAqB,EAAE,CAAC;SAC9B;QAED,eAAe;QACf,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED;;;;;;OAMG;IACK,oBAAoB;QAC1B,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAE/D,gBAAgB;QAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;QACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;QAElE,cAAc;QACd,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;QAC7B,MAAM,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QAC1B,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO;YAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;QACnD,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ;YAAE,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEvD,wBAAwB;QACxB,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QACtC,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QAEtC,kBAAkB;QAClB,MAAM,MAAM,GAAG,WAAW,GAAG,KAAK,CAAC;QACnC,MAAM,MAAM,GAAG,YAAY,GAAG,MAAM,CAAC;QAErC,0BAA0B;QAC1B,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAE1E,yBAAyB;QACzB,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,eAAgB,CAAC;QACtD,IAAI,YAAY,CAAC,KAAK,KAAK,OAAO;YAAE,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC;QACjE,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ;YAAE,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC;QACrE,YAAY,CAAC,SAAS,GAAG,eAAe,EAAE,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACK,qBAAqB;QAC3B,MAAM,EACJ,MAAM,EACN,YAAY,EACZ,OAAO,EACP,aAAa,EACb,cAAc,EACd,KAAK,EACL,MAAM,GACP,GAAG,IAAI,CAAC;QACT,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAEvC,cAAc;QACd,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;QAC7B,MAAM,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC;QAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;QAC1B,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO;YAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;QACnD,IAAI,KAAK,CAAC,MAAM,KAAK,QAAQ;YAAE,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEvD,mBAAmB;QACnB,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QACtC,MAAM,UAAU,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;QACtC,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAE1D,UAAU;QACV,IAAI,CAAC,IAAI,CAAC,eAAe;YAAE,OAAO;QAClC,IAAI,EACF,CAAC,EAAE,QAAQ,EACX,CAAC,EAAE,QAAQ,EACX,KAAK,EAAE,YAAY,EACnB,MAAM,EAAE,aAAa,GACtB,GAAG,aAAa,CAAC;QAElB,mBAAmB;QACnB,IAAI,QAAQ,GAAG,QAAQ,CAAC;QACxB,QAAQ,MAAM,EAAE;YACd,KAAK,qBAAqB,CAAC,IAAI;gBAC7B,qBAAqB;gBACrB,MAAM;YACR,KAAK,qBAAqB,CAAC,OAAO;gBAChC,YAAY,GAAG,KAAK,CAAC;gBACrB,aAAa,GAAG,MAAM,CAAC;gBACvB,MAAM;YACR,KAAK,qBAAqB,CAAC,IAAI;gBAC7B,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC;gBACpC,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;gBACtC,MAAM;YACR,KAAK,qBAAqB,CAAC,IAAI;gBAC7B,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC;gBACpC,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;gBACtC,QAAQ,GAAG,SAAS,CAAC;gBACrB,MAAM;SACT;QAED,QAAQ;QACR,QAAQ,KAAK,EAAE;YACb,KAAK,oBAAoB,CAAC,OAAO;gBAC/B,QAAQ,GAAG,CAAC,CAAC;gBACb,QAAQ,GAAG,CAAC,CAAC;gBACb,MAAM;YACR,KAAK,oBAAoB,CAAC,UAAU;gBAClC,QAAQ,GAAG,CAAC,CAAC;gBACb,QAAQ,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,oBAAoB,CAAC,UAAU;gBAClC,QAAQ,GAAG,CAAC,CAAC;gBACb,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC;gBAClC,MAAM;YACR,KAAK,oBAAoB,CAAC,QAAQ;gBAChC,QAAQ,GAAG,KAAK,GAAG,YAAY,CAAC;gBAChC,QAAQ,GAAG,CAAC,CAAC;gBACb,MAAM;YACR,KAAK,oBAAoB,CAAC,WAAW;gBACnC,QAAQ,GAAG,KAAK,GAAG,YAAY,CAAC;gBAChC,QAAQ,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,oBAAoB,CAAC,WAAW;gBACnC,QAAQ,GAAG,KAAK,GAAG,YAAY,CAAC;gBAChC,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC;gBAClC,MAAM;YACR,KAAK,oBAAoB,CAAC,SAAS;gBACjC,QAAQ,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtC,QAAQ,GAAG,CAAC,CAAC;gBACb,MAAM;YACR,KAAK,oBAAoB,CAAC,IAAI,CAAC;YAC/B,KAAK,oBAAoB,CAAC,YAAY;gBACpC,QAAQ,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtC,QAAQ,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,oBAAoB,CAAC,YAAY;gBACpC,QAAQ,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtC,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC;gBAClC,MAAM;SACT;QAED,sBAAsB;QACtB,MAAM,cAAc,GAAG,YAAY,GAAG,IAAI,CAAC;QAC3C,MAAM,eAAe,GAAG,aAAa,GAAG,IAAI,CAAC;QAC7C,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC;QACrD,IAAI,YAAY,CAAC,KAAK,KAAK,OAAO;YAAE,YAAY,CAAC,KAAK,GAAG,cAAc,CAAC;QACxE,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ;YAAE,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC;QAE5E,2BAA2B;QAC3B,YAAY,CAAC,SAAS,GAAG,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE7D,kBAAkB;QAClB,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACvC,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;SACnC;IACH,CAAC;IAEO,WAAW;QACjB,kCAAkC;QAClC,qBAAqB;QACrB,sEAAsE;IACxE,CAAC;CACF", "sourcesContent": ["import { RNSharedElementStyle } from \"./RNSharedElementStyle.web\";\nimport { Rect } from \"./Rect.web\";\nimport {\n  IHTMLElement,\n  RNSharedElementAlign,\n  RNSharedElementResize,\n} from \"./types\";\n\nfunction initElement(element: IHTMLElement): IHTMLElement {\n  const { style } = element;\n\n  // Reset default layout behavior\n  style.position = \"absolute\";\n  style.left = \"0px\";\n  style.top = \"0px\";\n  style.pointerEvents = \"none\";\n  style.transformOrigin = \"0% 0%\";\n  style.transformStyle = \"preserve-3d\";\n  style.backfaceVisibility = \"hidden\";\n  style.backgroundSize = \"100% 100%\";\n  style.boxSizing = \"border-box\";\n  style.overflow = \"hidden\";\n\n  // Clear styles\n  // style.backgroundColor = null;\n\n  return element;\n}\n\nfunction createTransform(\n  translateX: number = 0,\n  translateY: number = 0,\n  scaleX: number = 1,\n  scaleY: number = 1\n) {\n  return `matrix3d(\n    ${scaleX}, 0, 0, 0,\n    0, ${scaleY}, 0, 0,\n    0, 0, 1, 0,\n    ${translateX}, ${translateY}, 0, 1\n)`;\n}\n\nexport class RNSharedElementView {\n  public parentLayout: Rect = Rect.empty;\n  public readonly element = initElement(document.createElement(\"div\"));\n  public layout: Rect = Rect.empty;\n  public style: RNSharedElementStyle | null = null;\n  public originalLayout: Rect = Rect.empty;\n  public _contentElement: IHTMLElement | null = null;\n  public contentLayout: Rect = Rect.empty;\n  public resize: RNSharedElementResize = RNSharedElementResize.Auto;\n  public align: RNSharedElementAlign = RNSharedElementAlign.Auto;\n\n  get contentElement(): IHTMLElement | null {\n    return this._contentElement;\n  }\n  set contentElement(value: IHTMLElement | null) {\n    if (this._contentElement === value) return;\n    if (this._contentElement) {\n      this.element.removeChild(this._contentElement);\n    }\n    this._contentElement = value ? initElement(value) : null;\n    if (this._contentElement) {\n      this.element.appendChild(this._contentElement);\n    }\n  }\n\n  public updateLayout() {\n    const { layout, contentLayout, resize } = this;\n    if (layout.equals(Rect.empty)) return;\n\n    // Run either the stretch (scale) or resize algorithm\n    if (\n      resize === RNSharedElementResize.Stretch ||\n      (resize === RNSharedElementResize.Auto && contentLayout.equals(layout))\n    ) {\n      this.updateLayoutForScale();\n    } else {\n      this.updateLayoutForResize();\n    }\n\n    // Update style\n    this.updateStyle();\n  }\n\n  /**\n   * Updates the layout by only changing the scale of the\n   * element. This technique achieves a very high performance\n   * as it can be handled completely by the GPU, requiring\n   * no layout passes in the browser. It is however also more\n   * limited and can't be used for all effects.\n   */\n  private updateLayoutForScale() {\n    const { layout, originalLayout, parentLayout, element } = this;\n\n    // Update layout\n    const { width, height } = originalLayout;\n    const { x, y, width: scaledWidth, height: scaledHeight } = layout;\n\n    // Update size\n    const widthPx = width + \"px\";\n    const heightPx = height + \"px\";\n    const { style } = element;\n    if (style.width !== widthPx) style.width = widthPx;\n    if (style.height !== heightPx) style.height = heightPx;\n\n    // Calculate translation\n    const translateX = x - parentLayout.x;\n    const translateY = y - parentLayout.y;\n\n    // Calculate scale\n    const scaleX = scaledWidth / width;\n    const scaleY = scaledHeight / height;\n\n    // Update transform matrix\n    style.transform = createTransform(translateX, translateY, scaleX, scaleY);\n\n    // Update content element\n    const { style: contentStyle } = this._contentElement!;\n    if (contentStyle.width !== widthPx) contentStyle.width = widthPx;\n    if (contentStyle.height !== heightPx) contentStyle.height = heightPx;\n    contentStyle.transform = createTransform();\n  }\n\n  /**\n   * Updates the layout by updating the size of the\n   * element and its content element. This algorihm\n   * can achieve any possible layout, as well as\n   * clipping of the content.\n   */\n  private updateLayoutForResize() {\n    const {\n      layout,\n      parentLayout,\n      element,\n      contentLayout,\n      originalLayout,\n      align,\n      resize,\n    } = this;\n    const { x, y, width, height } = layout;\n\n    // Update size\n    const widthPx = width + \"px\";\n    const heightPx = height + \"px\";\n    const { style } = element;\n    if (style.width !== widthPx) style.width = widthPx;\n    if (style.height !== heightPx) style.height = heightPx;\n\n    // Update transform\n    const translateX = x - parentLayout.x;\n    const translateY = y - parentLayout.y;\n    style.transform = createTransform(translateX, translateY);\n\n    // Content\n    if (!this._contentElement) return;\n    let {\n      x: contentX,\n      y: contentY,\n      width: contentWidth,\n      height: contentHeight,\n    } = contentLayout;\n\n    // Get content size\n    let overflow = \"hidden\";\n    switch (resize) {\n      case RNSharedElementResize.Auto:\n        // keep original size\n        break;\n      case RNSharedElementResize.Stretch:\n        contentWidth = width;\n        contentHeight = height;\n        break;\n      case RNSharedElementResize.Clip:\n        contentWidth = originalLayout.width;\n        contentHeight = originalLayout.height;\n        break;\n      case RNSharedElementResize.None:\n        contentWidth = originalLayout.width;\n        contentHeight = originalLayout.height;\n        overflow = \"visible\";\n        break;\n    }\n\n    // Align\n    switch (align) {\n      case RNSharedElementAlign.LeftTop:\n        contentX = 0;\n        contentY = 0;\n        break;\n      case RNSharedElementAlign.LeftCenter:\n        contentX = 0;\n        contentY = (height - contentHeight) / 2;\n        break;\n      case RNSharedElementAlign.LeftBottom:\n        contentX = 0;\n        contentY = height - contentHeight;\n        break;\n      case RNSharedElementAlign.RightTop:\n        contentX = width - contentWidth;\n        contentY = 0;\n        break;\n      case RNSharedElementAlign.RightCenter:\n        contentX = width - contentWidth;\n        contentY = (height - contentHeight) / 2;\n        break;\n      case RNSharedElementAlign.RightBottom:\n        contentX = width - contentWidth;\n        contentY = height - contentHeight;\n        break;\n      case RNSharedElementAlign.CenterTop:\n        contentX = (width - contentWidth) / 2;\n        contentY = 0;\n        break;\n      case RNSharedElementAlign.Auto:\n      case RNSharedElementAlign.CenterCenter:\n        contentX = (width - contentWidth) / 2;\n        contentY = (height - contentHeight) / 2;\n        break;\n      case RNSharedElementAlign.CenterBottom:\n        contentX = (width - contentWidth) / 2;\n        contentY = height - contentHeight;\n        break;\n    }\n\n    // Update content size\n    const contentWidthPx = contentWidth + \"px\";\n    const contentHeightPx = contentHeight + \"px\";\n    const { style: contentStyle } = this._contentElement;\n    if (contentStyle.width !== widthPx) contentStyle.width = contentWidthPx;\n    if (contentStyle.height !== heightPx) contentStyle.height = contentHeightPx;\n\n    // Update content transform\n    contentStyle.transform = createTransform(contentX, contentY);\n\n    // Update overflow\n    if (element.style.overflow !== overflow) {\n      element.style.overflow = overflow;\n    }\n  }\n\n  private updateStyle() {\n    //const { style, element } = this;\n    //if (!style) return;\n    // element.style.backgroundColor = formatColor(style.backgroundColor);\n  }\n}\n"]}