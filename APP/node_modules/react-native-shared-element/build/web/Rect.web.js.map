{"version": 3, "file": "Rect.web.js", "sourceRoot": "", "sources": ["../../src/web/Rect.web.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,IAAI;IACR,CAAC,GAAW,CAAC,CAAC;IACd,CAAC,GAAW,CAAC,CAAC;IACd,KAAK,GAAW,CAAC,CAAC;IAClB,MAAM,GAAW,CAAC,CAAC;IAEnB,MAAM,CAAU,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IAE1C,YAAY,MAAc;QACxB,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SAC7B;IACH,CAAC;IAEM,MAAM,CAAC,IAAU;QACtB,OAAO,CACL,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YACjB,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC;YACjB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK;YACzB,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAC5B,CAAC;IACJ,CAAC", "sourcesContent": ["import { IRect } from \"./types\";\n\nexport class Rect implements IRect {\n  public x: number = 0;\n  public y: number = 0;\n  public width: number = 0;\n  public height: number = 0;\n\n  public static readonly empty = new Rect();\n\n  constructor(source?: IRect) {\n    if (source) {\n      this.x = source.x;\n      this.y = source.y;\n      this.width = source.width;\n      this.height = source.height;\n    }\n  }\n\n  public equals(rect: Rect): boolean {\n    return (\n      this.x === rect.x &&\n      this.y === rect.y &&\n      this.width === rect.width &&\n      this.height === rect.height\n    );\n  }\n}\n"]}