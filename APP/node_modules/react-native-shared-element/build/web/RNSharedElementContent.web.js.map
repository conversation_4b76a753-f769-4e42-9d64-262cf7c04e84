{"version": 3, "file": "RNSharedElementContent.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementContent.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAGlC,MAAM,OAAO,sBAAsB;IACjB,OAAO,CAAe;IACtB,IAAI,CAAO;IAE3B,YAAY,OAAqB,EAAE,IAAU;QAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAqB;QAClC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE;gBACjC,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;gBAC1C,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;oBAChB,OAAO,CACL,IAAI,IAAI,CAAC;wBACP,CAAC,EAAE,CAAC;wBACJ,CAAC,EAAE,CAAC;wBACJ,KAAK,EAAE,GAAG,CAAC,KAAK;wBAChB,MAAM,EAAE,GAAG,CAAC,MAAM;qBACnB,CAAC,CACH,CAAC;gBACJ,CAAC,CAAC;gBACF,GAAG,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClC,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC;gBAC1C,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3C,OAAO;aACR;YACD,OAAO,CACL,IAAI,IAAI,CAAC;gBACP,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;gBACJ,KAAK,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;gBAC/B,MAAM,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC;aAClC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,SAAS,CACd,MAAY,EACZ,OAAsC,EACtC,UAAkB,EAClB,OAAiB;QAEjB,IAAI,CAAC,OAAO;YAAE,OAAO,MAAM,CAAC;QAC5B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe;YAAE,OAAO,MAAM,CAAC;QAC1D,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAC/B,MAAM,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;QACpE,MAAM,EAAE,GAAG,KAAK,GAAG,MAAM,GAAG,kBAAkB,CAAC;QAC/C,MAAM,mBAAmB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC/C,QAAQ,UAAU,EAAE;YAClB,KAAK,SAAS,CAAC;YACf,KAAK,WAAW;gBACd,MAAM;gBACN,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,mBAAmB,EAAE;oBACvB,KAAK,GAAG,MAAM,GAAG,kBAAkB,CAAC;iBACrC;qBAAM;oBACL,MAAM,GAAG,KAAK,GAAG,kBAAkB,CAAC;iBACrC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;gBAC3B,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC7B,MAAM;YACR,KAAK,SAAS,CAAC;YACf;gBACE,IAAI,mBAAmB,EAAE;oBACvB,MAAM,GAAG,KAAK,GAAG,kBAAkB,CAAC;iBACrC;qBAAM;oBACL,KAAK,GAAG,MAAM,GAAG,kBAAkB,CAAC;iBACrC;gBACD,MAAM;SACT;QACD,OAAO,IAAI,IAAI,CAAC;YACd,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;YACxC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;YAC1C,KAAK;YACL,MAAM;SACP,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["import { Rect } from \"./Rect.web\";\nimport { IHTMLElement } from \"./types\";\n\nexport class RNSharedElementContent {\n  public readonly element: IHTMLElement;\n  public readonly size: Rect;\n\n  constructor(element: IHTMLElement, size: Rect) {\n    this.element = element;\n    this.size = size;\n  }\n\n  static getSize(element: IHTMLElement): Promise<Rect | null> {\n    return new Promise((resolve) => {\n      if (element.style.backgroundImage) {\n        const img = document.createElement(\"img\");\n        img.onload = () => {\n          resolve(\n            new Rect({\n              x: 0,\n              y: 0,\n              width: img.width,\n              height: img.height,\n            })\n          );\n        };\n        img.onerror = () => resolve(null);\n        const url = element.style.backgroundImage;\n        img.src = url.substring(5, url.length - 2);\n        return;\n      }\n      resolve(\n        new Rect({\n          x: 0,\n          y: 0,\n          width: element.clientWidth || 0,\n          height: element.clientHeight || 0,\n        })\n      );\n    });\n  }\n\n  static getLayout(\n    layout: Rect,\n    content: RNSharedElementContent | null,\n    resizeMode: string,\n    reverse?: boolean\n  ) {\n    if (!content) return layout;\n    if (!content.element.style.backgroundImage) return layout;\n    let { width, height } = layout;\n    const contentAspectRatio = content.size.width / content.size.height;\n    const lo = width / height < contentAspectRatio;\n    const aspectRatioCriteria = reverse ? !lo : lo;\n    switch (resizeMode) {\n      case \"stretch\":\n      case \"100% 100%\":\n        // nop\n        break;\n      case \"cover\":\n        if (aspectRatioCriteria) {\n          width = height * contentAspectRatio;\n        } else {\n          height = width / contentAspectRatio;\n        }\n        break;\n      case \"center\":\n        width = content.size.width;\n        height = content.size.height;\n        break;\n      case \"contain\":\n      default:\n        if (aspectRatioCriteria) {\n          height = width / contentAspectRatio;\n        } else {\n          width = height * contentAspectRatio;\n        }\n        break;\n    }\n    return new Rect({\n      x: layout.x + (layout.width - width) / 2,\n      y: layout.y + (layout.height - height) / 2,\n      width,\n      height,\n    });\n  }\n}\n"]}