{"version": 3, "file": "RNSharedElementStyle.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementStyle.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,UAAU,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAClE,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAGlC;;;;;;;;;0BAS0B;AAE1B,SAAS,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB;IAC/D,OAAO,IAAI,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,QAAQ,CAAC;AACzC,CAAC;AAED,MAAM,OAAO,oBAAoB;IACf,MAAM,CAAO;IACb,KAAK,CAAsB;IAC3B,OAAO,CAAS;IAChB,eAAe,CAAQ;IAEvC,YAAY,MAAY,EAAE,KAA0B;QAClD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACrC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAC3D,CAAC;IAEM,MAAM,CAAC,qBAAqB,CACjC,OAAa,EACb,OAAa,EACb,QAAgB;QAEhB,OAAO,IAAI,IAAI,CAAC;YACd,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC;YAC9C,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC;YAC9C,KAAK,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;YAC1D,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAChC,MAA4B,EAC5B,MAA4B,EAC5B,QAAgB;QAEhB,MAAM,MAAM,GAAG,oBAAoB,CAAC,qBAAqB,CACvD,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,MAAM,EACb,QAAQ,CACT,CAAC;QACF,OAAO,IAAI,oBAAoB,CAAC,MAAM,EAAE;YACtC,GAAG,MAAM;YACT,OAAO,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC;YAC9D,eAAe,EAAE,gBAAgB,CAC/B,MAAM,CAAC,eAAe,EACtB,MAAM,CAAC,eAAe,EACtB,QAAQ,CACT;SACF,CAAC,CAAC;IACL,CAAC;CAyBF", "sourcesContent": ["import { Color, parseColor, interpolateColor } from \"./Color.web\";\nimport { Rect } from \"./Rect.web\";\nimport { CSSStyleDeclaration } from \"./types\";\n\n/*int backgroundColor = Color.TRANSPARENT;\n    float opacity = 1;\n    float borderTopLeftRadius = 0;\n    float borderTopRightRadius = 0;\n    float borderBottomLeftRadius = 0;\n    float borderBottomRightRadius = 0;\n    float borderWidth = 0;\n    int borderColor = Color.TRANSPARENT;\n    String borderStyle = \"solid\";\n    float elevation = 0;*/\n\nfunction interpolate(val1: number, val2: number, position: number) {\n  return val1 + (val2 - val1) * position;\n}\n\nexport class RNSharedElementStyle {\n  public readonly layout: Rect;\n  public readonly style: CSSStyleDeclaration;\n  public readonly opacity: number;\n  public readonly backgroundColor: Color;\n\n  constructor(layout: Rect, style: CSSStyleDeclaration) {\n    this.layout = layout;\n    this.style = style;\n    this.opacity = Number(style.opacity);\n    this.backgroundColor = parseColor(style.backgroundColor);\n  }\n\n  public static getInterpolatedLayout(\n    layout1: Rect,\n    layout2: Rect,\n    position: number\n  ) {\n    return new Rect({\n      x: interpolate(layout1.x, layout2.x, position),\n      y: interpolate(layout1.y, layout2.y, position),\n      width: interpolate(layout1.width, layout2.width, position),\n      height: interpolate(layout1.height, layout2.height, position),\n    });\n  }\n\n  public static getInterpolatedStyle(\n    style1: RNSharedElementStyle,\n    style2: RNSharedElementStyle,\n    position: number\n  ) {\n    const layout = RNSharedElementStyle.getInterpolatedLayout(\n      style1.layout,\n      style2.layout,\n      position\n    );\n    return new RNSharedElementStyle(layout, {\n      ...style1,\n      opacity: interpolate(style1.opacity, style2.opacity, position),\n      backgroundColor: interpolateColor(\n        style1.backgroundColor,\n        style2.backgroundColor,\n        position\n      ),\n    });\n  }\n\n  /* \n\n    private RNSharedElementStyle getInterpolatedStyle(\n        RNSharedElementStyle style1,\n        RNSharedElementContent content1,\n        RNSharedElementStyle style2,\n        RNSharedElementContent content2,\n        float position\n    ) {\n        RNSharedElementStyle result = new RNSharedElementStyle();\n        result.scaleType = RNSharedElementStyle.getInterpolatingScaleType(style1, style2, position);\n        result.opacity = style1.opacity + ((style2.opacity - style1.opacity) * position);\n        result.backgroundColor = getInterpolatedColor(style1.backgroundColor, style2.backgroundColor, position);\n        result.borderTopLeftRadius = style1.borderTopLeftRadius + ((style2.borderTopLeftRadius - style1.borderTopLeftRadius) * position);\n        result.borderTopRightRadius = style1.borderTopRightRadius + ((style2.borderTopRightRadius - style1.borderTopRightRadius) * position);\n        result.borderBottomLeftRadius = style1.borderBottomLeftRadius + ((style2.borderBottomLeftRadius - style1.borderBottomLeftRadius) * position);\n        result.borderBottomRightRadius = style1.borderBottomRightRadius + ((style2.borderBottomRightRadius - style1.borderBottomRightRadius) * position);\n        result.borderWidth = style1.borderWidth + ((style2.borderWidth - style1.borderWidth) * position);\n        result.borderColor = getInterpolatedColor(style1.borderColor, style2.borderColor, position);\n        result.borderStyle = style1.borderStyle;\n        result.elevation = style1.elevation + ((style2.elevation - style1.elevation) * position);\n        return result;\n    }*/\n}\n"]}