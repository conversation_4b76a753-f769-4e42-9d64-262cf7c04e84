{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/web/types.ts"], "names": [], "mappings": "AAiBA,MAAM,CAAN,IAAY,wBAKX;AALD,WAAY,wBAAwB;IAClC,uEAAQ,CAAA;IACR,uEAAQ,CAAA;IACR,2EAAU,CAAA;IACV,6EAAW,CAAA;AACb,CAAC,EALW,wBAAwB,KAAxB,wBAAwB,QAKnC;AAED,MAAM,CAAN,IAAY,qBAKX;AALD,WAAY,qBAAqB;IAC/B,iEAAQ,CAAA;IACR,uEAAW,CAAA;IACX,iEAAQ,CAAA;IACR,iEAAQ,CAAA;AACV,CAAC,EALW,qBAAqB,KAArB,qBAAqB,QAKhC;AAED,MAAM,CAAN,IAAY,oBAWX;AAXD,WAAY,oBAAoB;IAC9B,+DAAQ,CAAA;IACR,qEAAW,CAAA;IACX,2EAAc,CAAA;IACd,2EAAc,CAAA;IACd,uEAAY,CAAA;IACZ,6EAAe,CAAA;IACf,6EAAe,CAAA;IACf,yEAAa,CAAA;IACb,+EAAgB,CAAA;IAChB,+EAAgB,CAAA;AAClB,CAAC,EAXW,oBAAoB,KAApB,oBAAoB,QAW/B", "sourcesContent": ["export interface IRect {\n  readonly x: number;\n  readonly y: number;\n  readonly width: number;\n  readonly height: number;\n}\n\nexport type CSSStyleDeclaration = any;\n\nexport type IHTMLElement = HTMLElement;\n\nexport type RNSharedElementNodeConfig = {\n  nodeHandle: IHTMLElement;\n  isParent: boolean;\n  nodeStyle: any;\n};\n\nexport enum RNSharedElementAnimation {\n  Move = 0,\n  Fade = 1,\n  FadeIn = 2,\n  FadeOut = 3,\n}\n\nexport enum RNSharedElementResize {\n  Auto = 0,\n  Stretch = 1,\n  Clip = 2,\n  None = 3,\n}\n\nexport enum RNSharedElementAlign {\n  Auto = 0,\n  LeftTop = 1,\n  LeftCenter = 2,\n  LeftBottom = 3,\n  RightTop = 4,\n  RightCenter = 5,\n  RightBottom = 6,\n  CenterTop = 7,\n  CenterCenter = 8,\n  CenterBottom = 9,\n}\n"]}