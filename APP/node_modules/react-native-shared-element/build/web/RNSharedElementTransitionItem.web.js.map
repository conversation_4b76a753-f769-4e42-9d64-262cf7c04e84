{"version": 3, "file": "RNSharedElementTransitionItem.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementTransitionItem.web.ts"], "names": [], "mappings": "AAKA,MAAM,OAAO,6BAA6B;IAChC,OAAO,GAAY,KAAK,CAAC;IACjB,IAAI,CAAS;IACrB,KAAK,GAA+B,IAAI,CAAC;IACzC,YAAY,CAA6B;IAC1C,UAAU,GAAY,KAAK,CAAC;IAC5B,KAAK,GAAgC,IAAI,CAAC;IAC1C,YAAY,GAAY,KAAK,CAAC;IAC9B,OAAO,GAAkC,IAAI,CAAC;IAErD,YAAY,WAAuC,EAAE,IAAY;QAC/D,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IACD,IAAI,IAAI,CAAC,IAAgC;QACvC,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACvB,IAAI,IAAI,IAAI,IAAI;gBAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAClD,OAAO;SACR;QACD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtB,IAAI,IAAI,CAAC,OAAO;gBAAE,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvC;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,IAAI,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,IAAI,IAAI,IAAI,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACD,IAAI,MAAM,CAAC,MAAe;QACxB,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YAAE,OAAO;QACpC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,KAAK;YAAE,OAAO;QACxB,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;SAC7B;IACH,CAAC;CACF", "sourcesContent": ["import { RNSharedElementContent } from \"./RNSharedElementContent.web\";\nimport { RNSharedElementNode } from \"./RNSharedElementNode.web\";\nimport { RNSharedElementNodeManager } from \"./RNSharedElementNodeManager.web\";\nimport { RNSharedElementStyle } from \"./RNSharedElementStyle.web\";\n\nexport class RNSharedElementTransitionItem {\n  private _hidden: boolean = false;\n  public readonly name: string;\n  private _node: RNSharedElementNode | null = null;\n  private _nodeManager: RNSharedElementNodeManager;\n  public needsStyle: boolean = false;\n  public style: RNSharedElementStyle | null = null;\n  public needsContent: boolean = false;\n  public content: RNSharedElementContent | null = null;\n\n  constructor(nodeManager: RNSharedElementNodeManager, name: string) {\n    this._nodeManager = nodeManager;\n    this.name = name;\n  }\n\n  get node(): RNSharedElementNode | null {\n    return this._node;\n  }\n  set node(node: RNSharedElementNode | null) {\n    if (this._node === node) {\n      if (node != null) this._nodeManager.release(node);\n      return;\n    }\n    if (this._node != null) {\n      if (this._hidden) this._node.releaseHideRef();\n      this._nodeManager.release(this._node);\n    }\n    this._node = node;\n    this._hidden = false;\n    this.needsStyle = node != null;\n    this.style = null;\n    this.needsContent = node != null;\n    this.content = null;\n  }\n\n  get hidden(): boolean {\n    return this._hidden;\n  }\n  set hidden(hidden: boolean) {\n    if (this._hidden === hidden) return;\n    this._hidden = hidden;\n    if (!this._node) return;\n    if (hidden) {\n      this._node.addHideRef();\n    } else {\n      this._node.releaseHideRef();\n    }\n  }\n}\n"]}