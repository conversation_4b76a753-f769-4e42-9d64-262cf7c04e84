{"version": 3, "file": "RNSharedElementTransition.web.js", "sourceRoot": "", "sources": ["../../src/web/RNSharedElementTransition.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AACtE,OAAO,EAAE,0BAA0B,EAAE,MAAM,kCAAkC,CAAC;AAC9E,OAAO,EAAE,oBAAoB,EAAE,MAAM,4BAA4B,CAAC;AAClE,OAAO,EAAE,6BAA6B,EAAE,MAAM,qCAAqC,CAAC;AACpF,+DAA+D;AAC/D,OAAO,EAAE,mBAAmB,EAAE,MAAM,2BAA2B,CAAC;AAChE,OAAO,EAAE,IAAI,EAAE,MAAM,YAAY,CAAC;AAClC,OAAO,EAEL,wBAAwB,EACxB,qBAAqB,EACrB,oBAAoB,GAErB,MAAM,SAAS,CAAC;AAEjB,MAAM,OAAO,yBAAyB;IAC5B,KAAK,GAAG;QACd,IAAI,6BAA6B,CAC/B,0BAA0B,CAAC,WAAW,EAAE,EACxC,OAAO,CACR;QACD,IAAI,6BAA6B,CAC/B,0BAA0B,CAAC,WAAW,EAAE,EACxC,KAAK,CACN;KACF,CAAC;IACK,SAAS,GAA6B,wBAAwB,CAAC,IAAI,CAAC;IACpE,MAAM,GAA0B,qBAAqB,CAAC,IAAI,CAAC;IAC3D,KAAK,GAAyB,oBAAoB,CAAC,IAAI,CAAC;IACxD,YAAY,GAAW,CAAC,CAAC;IACzB,OAAO,GAAwB,IAAI,CAAC;IACnC,MAAM,GAAS,IAAI,CAAC,KAAK,CAAC;IAC1B,KAAK,GAAmC,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAEtD,OAAO;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;IACnD,CAAC;IAEM,OAAO,CACZ,GAAY,EACZ,IAAsC,EACtC,QAA0C;QAE1C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;YAC1B,IAAI,IAAI,QAAQ;gBACd,CAAC,CAAC,0BAA0B,CAAC,WAAW,EAAE,CAAC,OAAO,CAC9C,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,QAAQ,EACb,QAAQ,CAAC,UAAU,CACpB;gBACH,CAAC,CAAC,IAAI,CAAC;IACb,CAAC;IAEM,WAAW;QAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAEO,uBAAuB;QAC7B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;gBACxB,IAAI,CAAC,IAAK,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;oBACnB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC;aACJ;YACD,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;gBAC1B,IAAI,CAAC,IAAK,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;oBAC3C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;oBACvB,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,CAAC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB;QAC1B,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;QAClC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACrB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,IACE,MAAM;gBACN,SAAS,KAAK,wBAAwB,CAAC,MAAM;gBAC7C,IAAI,CAAC,IAAI,KAAK,OAAO;gBAErB,MAAM,GAAG,KAAK,CAAC;YACjB,IACE,MAAM;gBACN,SAAS,KAAK,wBAAwB,CAAC,OAAO;gBAC9C,IAAI,CAAC,IAAI,KAAK,KAAK;gBAEnB,MAAM,GAAG,KAAK,CAAC;YACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,YAAY;QAClB,MAAM,EACJ,OAAO,EACP,KAAK,EACL,YAAY,EACZ,SAAS,CAAC,8BAA8B,GACzC,GAAG,IAAI,CAAC;QACT,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,oBAAoB;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC;QAExD,uBAAuB;QACvB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAChC,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ;YAAE,OAAO;QACrC,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QACtC,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAEpC,mBAAmB;QACnB,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAChE,MAAM,kBAAkB,GAAG,UAAU;YACnC,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAC9B,WAAW,EACX,YAAY,EACZ,UAAU,CAAC,KAAK,CAAC,cAAc,CAChC;YACH,CAAC,CAAC,WAAW,CAAC;QAChB,yJAAyJ;QACzJ,mGAAmG;QAEnG,iBAAiB;QACjB,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAC1D,MAAM,gBAAgB,GAAG,QAAQ;YAC/B,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAC9B,SAAS,EACT,UAAU,IAAI,YAAY,EAC1B,QAAQ,CAAC,KAAK,CAAC,cAAc,CAC9B;YACH,CAAC,CAAC,SAAS,CAAC;QAEd,0BAA0B;QAC1B,IAAI,kBAAkB,GAAS,WAAW,CAAC;QAC3C,IAAI,iBAAiB,GAAyB,UAAW,CAAC;QAC1D,IAAI,yBAAyB,GAAS,kBAAkB,CAAC;QACzD,iDAAiD;QACjD,IAAI,UAAU,IAAI,QAAQ,EAAE;YAC1B,kBAAkB,GAAG,oBAAoB,CAAC,qBAAqB,CAC7D,WAAW,EACX,SAAS,EACT,YAAY,CACb,CAAC;YACF,yJAAyJ;YACzJ,iBAAiB,GAAG,oBAAoB,CAAC,oBAAoB,CAC3D,UAAU,EACV,QAAQ,EACR,YAAY,CACb,CAAC;YACF,yBAAyB,GAAG,oBAAoB,CAAC,qBAAqB,CACpE,kBAAkB,EAClB,gBAAgB,EAChB,YAAY,CACb,CAAC;SACH;aAAM,IAAI,UAAU,EAAE;YACrB,kBAAkB,GAAG,WAAW,CAAC;YACjC,iBAAiB,GAAG,UAAU,CAAC;YAC/B,yBAAyB,GAAG,kBAAkB,CAAC;YAC/C,4CAA4C;SAC7C;aAAM;YACL,kBAAkB,GAAG,SAAS,CAAC;YAC/B,iBAAiB,GAAG,QAAS,CAAC;YAC9B,yBAAyB,GAAG,gBAAgB,CAAC;YAC7C,0CAA0C;SAC3C;QAED,IACE,SAAS,KAAK,wBAAwB,CAAC,IAAI;YAC3C,SAAS,KAAK,wBAAwB,CAAC,IAAI;YAC3C,SAAS,KAAK,wBAAwB,CAAC,OAAO,EAC9C;YACA,MAAM,YAAY,GAChB,SAAS,KAAK,wBAAwB,CAAC,IAAI;gBACzC,CAAC,CAAC,iBAAiB,CAAC,OAAO;gBAC3B,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;YACjE,IAAI,CAAC,UAAU,CACb,CAAC,EACD,kBAAkB,EAClB,iBAAiB,EACjB,yBAAyB,EACzB,WAAW,EACX,YAAY,EACZ,YAAY,CACb,CAAC;SACH;QACD,IACE,SAAS,KAAK,wBAAwB,CAAC,IAAI;YAC3C,SAAS,KAAK,wBAAwB,CAAC,MAAM,EAC7C;YACA,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;YACpE,IAAI,CAAC,UAAU,CACb,CAAC,EACD,kBAAkB,EAClB,iBAAiB,EACjB,yBAAyB,EACzB,SAAS,EACT,UAAU,EACV,UAAU,CACX,CAAC;SACH;IACH,CAAC;IAEO,UAAU,CAChB,KAAa,EACb,kBAAwB,EACxB,iBAAuC,EACvC,yBAA+B,EAC/B,cAAoB,EACpB,OAAsC,EACtC,OAAe;QAEf,qBAAqB;QACrB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,IAAI,EAAE;YACT,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;YACjC,IAAI,CAAC,OAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;SAC1B;QAED,qBAAqB;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAExB,cAAc;QACd,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,kBAAkB,CAAC;QACjC,IAAI,CAAC,aAAa,GAAG,yBAAyB,CAAC;QAE/C,YAAY;QACZ,IAAI,CAAC,KAAK,GAAG,iBAAiB,CAAC;QAE/B,kFAAkF;QAClF,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;gBAAE,OAAO;YACzC,aAAa;YACb,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,cAAe,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;QAErD,gBAAgB;QAChB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;CACF", "sourcesContent": ["import { RNSharedElementContent } from \"./RNSharedElementContent.web\";\nimport { RNSharedElementNodeManager } from \"./RNSharedElementNodeManager.web\";\nimport { RNSharedElementStyle } from \"./RNSharedElementStyle.web\";\nimport { RNSharedElementTransitionItem } from \"./RNSharedElementTransitionItem.web\";\n// import { RNSharedElementNode } from \"./RNSharedElementNode\";\nimport { RNSharedElementView } from \"./RNSharedElementView.web\";\nimport { Rect } from \"./Rect.web\";\nimport {\n  RNSharedElementNodeConfig,\n  RNSharedElementAnimation,\n  RNSharedElementResize,\n  RNSharedElementAlign,\n  IHTMLElement,\n} from \"./types\";\n\nexport class RNSharedElementTransition {\n  private items = [\n    new RNSharedElementTransitionItem(\n      RNSharedElementNodeManager.getInstance(),\n      \"start\"\n    ),\n    new RNSharedElementTransitionItem(\n      RNSharedElementNodeManager.getInstance(),\n      \"end\"\n    ),\n  ];\n  public animation: RNSharedElementAnimation = RNSharedElementAnimation.Move;\n  public resize: RNSharedElementResize = RNSharedElementResize.Auto;\n  public align: RNSharedElementAlign = RNSharedElementAlign.Auto;\n  public nodePosition: number = 0;\n  public element: IHTMLElement | null = null;\n  private layout: Rect = Rect.empty;\n  private views: (RNSharedElementView | null)[] = [null, null];\n\n  public destroy() {\n    this.element = null;\n    this.items.forEach((item) => (item.node = null));\n  }\n\n  public setNode(\n    end: boolean,\n    node: RNSharedElementNodeConfig | null,\n    ancestor: RNSharedElementNodeConfig | null\n  ) {\n    this.items[end ? 1 : 0].node =\n      node && ancestor\n        ? RNSharedElementNodeManager.getInstance().acquire(\n            node.nodeHandle,\n            node.isParent,\n            ancestor.nodeHandle\n          )\n        : null;\n  }\n\n  public didSetProps() {\n    this.requestStylesAndContent();\n    this.updateLayout();\n    this.updateNodeVisibility();\n  }\n\n  private requestStylesAndContent() {\n    this.items.forEach((item) => {\n      if (item.needsStyle) {\n        item.needsStyle = false;\n        item.node!.requestStyle().then((style) => {\n          item.style = style;\n          this.updateLayout();\n        });\n      }\n      if (item.needsContent) {\n        item.needsContent = false;\n        item.node!.requestContent().then((content) => {\n          item.content = content;\n          this.updateLayout();\n        });\n      }\n    });\n  }\n\n  private updateNodeVisibility() {\n    const { items, animation } = this;\n    items.forEach((item) => {\n      let hidden = !!(item.style && item.content);\n      if (\n        hidden &&\n        animation === RNSharedElementAnimation.FadeIn &&\n        item.name === \"start\"\n      )\n        hidden = false;\n      if (\n        hidden &&\n        animation === RNSharedElementAnimation.FadeOut &&\n        item.name === \"end\"\n      )\n        hidden = false;\n      item.hidden = hidden;\n    });\n  }\n\n  private updateLayout() {\n    const {\n      element,\n      items,\n      nodePosition,\n      animation /*, animation, resize, align*/,\n    } = this;\n    if (!element) return;\n\n    // Get parent layout\n    this.layout = new Rect(element.getBoundingClientRect());\n\n    // Get styles & content\n    const startStyle = items[0].style;\n    const endStyle = items[1].style;\n    if (!startStyle && !endStyle) return;\n    const startContent = items[0].content;\n    const endContent = items[1].content;\n\n    // Get start layout\n    const startLayout = startStyle ? startStyle.layout : Rect.empty;\n    const startContentLayout = startStyle\n      ? RNSharedElementContent.getLayout(\n          startLayout,\n          startContent,\n          startStyle.style.backgroundSize\n        )\n      : startLayout;\n    //CGRect startVisibleLayout = startStyle ? [self normalizeLayout:[startItem visibleLayoutForAncestor:startAncestor] ancestor:startAncestor] : CGRectZero;\n    //UIEdgeInsets startClipInsets = [self getClipInsets:startLayout visibleLayout:startVisibleLayout];\n\n    // Get end layout\n    const endLayout = endStyle ? endStyle.layout : Rect.empty;\n    const endContentLayout = endStyle\n      ? RNSharedElementContent.getLayout(\n          endLayout,\n          endContent || startContent,\n          endStyle.style.backgroundSize\n        )\n      : endLayout;\n\n    // Get interpolated layout\n    let interpolatedLayout: Rect = startLayout;\n    let interpolatedStyle: RNSharedElementStyle = startStyle!;\n    let interpolatedContentLayout: Rect = startContentLayout;\n    // let interpolatedClipInsets: Rect = Rect.empty;\n    if (startStyle && endStyle) {\n      interpolatedLayout = RNSharedElementStyle.getInterpolatedLayout(\n        startLayout,\n        endLayout,\n        nodePosition\n      );\n      // interpolatedClipInsets = getInterpolatedClipInsets(parentLayout, startClipInsets, startClippedLayout, endClipInsets, endClippedLayout, mNodePosition);\n      interpolatedStyle = RNSharedElementStyle.getInterpolatedStyle(\n        startStyle,\n        endStyle,\n        nodePosition\n      );\n      interpolatedContentLayout = RNSharedElementStyle.getInterpolatedLayout(\n        startContentLayout,\n        endContentLayout,\n        nodePosition\n      );\n    } else if (startStyle) {\n      interpolatedLayout = startLayout;\n      interpolatedStyle = startStyle;\n      interpolatedContentLayout = startContentLayout;\n      // interpolatedClipInsets = startClipInsets;\n    } else {\n      interpolatedLayout = endLayout;\n      interpolatedStyle = endStyle!;\n      interpolatedContentLayout = endContentLayout;\n      // interpolatedClipInsets = endClipInsets;\n    }\n\n    if (\n      animation === RNSharedElementAnimation.Move ||\n      animation === RNSharedElementAnimation.Fade ||\n      animation === RNSharedElementAnimation.FadeOut\n    ) {\n      const startOpacity =\n        animation === RNSharedElementAnimation.Move\n          ? interpolatedStyle.opacity\n          : (startStyle ? startStyle.opacity : 1) * (1 - nodePosition);\n      this.updateView(\n        0,\n        interpolatedLayout,\n        interpolatedStyle,\n        interpolatedContentLayout,\n        startLayout,\n        startContent,\n        startOpacity\n      );\n    }\n    if (\n      animation === RNSharedElementAnimation.Fade ||\n      animation === RNSharedElementAnimation.FadeIn\n    ) {\n      const endOpacity = (endStyle ? endStyle.opacity : 1) * nodePosition;\n      this.updateView(\n        1,\n        interpolatedLayout,\n        interpolatedStyle,\n        interpolatedContentLayout,\n        endLayout,\n        endContent,\n        endOpacity\n      );\n    }\n  }\n\n  private updateView(\n    index: number,\n    interpolatedLayout: Rect,\n    interpolatedStyle: RNSharedElementStyle,\n    interpolatedContentLayout: Rect,\n    originalLayout: Rect,\n    content: RNSharedElementContent | null,\n    opacity: number\n  ) {\n    // Find / create view\n    let view = this.views[index];\n    if (!view) {\n      view = new RNSharedElementView();\n      this.element!.appendChild(view.element);\n      this.views[index] = view;\n    }\n\n    // Set resize & align\n    view.resize = this.resize;\n    view.align = this.align;\n\n    // Set layouts\n    view.parentLayout = this.layout;\n    view.originalLayout = originalLayout;\n    view.layout = interpolatedLayout;\n    view.contentLayout = interpolatedContentLayout;\n\n    // Set style\n    view.style = interpolatedStyle;\n\n    // If the content-element does not yet exist, then clone it and add it to the view\n    if (!view.contentElement) {\n      if (!content || !content.element) return;\n      // @ts-ignore\n      view.contentElement = content.element.cloneNode(true);\n    }\n    view.contentElement!.style.opacity = String(opacity);\n\n    // Update layout\n    view.updateLayout();\n  }\n}\n"]}