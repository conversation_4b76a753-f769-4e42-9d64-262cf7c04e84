{"version": 3, "file": "RNSharedElementTransitionView.web.js", "sourceRoot": "", "sources": ["../src/RNSharedElementTransitionView.web.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAEpD,OAAO,EACL,yBAAyB,GAK1B,MAAM,iBAAiB,CAAC;AAqBzB,MAAM,OAAO,6BAA8B,SAAQ,KAAK,CAAC,SAGxD;IACC,KAAK,GAAG;QACN,UAAU,EAAE,IAAI,yBAAyB,EAAE;KAC5C,CAAC;IAEF,MAAM,CAAC,wBAAwB,CAAC,KAAgB,EAAE,KAAgB;QAChE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAClE,KAAK,CAAC;QACR,MAAM,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC;QAC7B,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC9D,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;QACzD,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;QACvC,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;QACjC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;QAC3B,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC;QACzB,UAAU,CAAC,WAAW,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qBAAqB;QACnB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,oBAAoB;QAClB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAEO,QAAQ,GAAG,CAAC,GAAQ,EAAE,EAAE;QAC9B,IAAI,CAAC,GAAG;YAAE,OAAO;QACjB,MAAM,OAAO,GAAQ,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;QAClC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC,CAAC;IAEF,MAAM;QACJ,uDAAuD;QACvD,OAAO,oBAAC,IAAI,IAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,GAAI,CAAC;IACtC,CAAC;CACF", "sourcesContent": ["import * as React from \"react\";\nimport { View, findNode<PERSON>andle } from \"react-native\";\n\nimport {\n  RNSharedElementTransition,\n  RNSharedElementNodeConfig,\n  RNSharedElementAnimation,\n  RNSharedElementResize,\n  RNSharedElementAlign,\n} from \"./web/index.web\";\n\ntype PropsType = {\n  startNode: {\n    node: RNSharedElementNodeConfig | null;\n    ancestor: RNSharedElementNodeConfig | null;\n  };\n  endNode: {\n    node: RNSharedElementNodeConfig | null;\n    ancestor: RNSharedElementNodeConfig | null;\n  };\n  nodePosition: number | any;\n  animation: RNSharedElementAnimation;\n  resize: RNSharedElementResize;\n  align: RNSharedElementAlign;\n  //onMeasure?: (event: SharedElementOnMeasureEvent) => void;\n};\ntype StateType = {\n  transition: RNSharedElementTransition;\n};\n\nexport class RNSharedElementTransitionView extends React.Component<\n  PropsType,\n  StateType\n> {\n  state = {\n    transition: new RNSharedElementTransition(),\n  };\n\n  static getDerivedStateFromProps(props: PropsType, state: StateType) {\n    const { startNode, endNode, animation, resize, align, nodePosition } =\n      props;\n    const { transition } = state;\n    transition.setNode(false, startNode.node, startNode.ancestor);\n    transition.setNode(true, endNode.node, endNode.ancestor);\n    transition.nodePosition = nodePosition;\n    transition.animation = animation;\n    transition.resize = resize;\n    transition.align = align;\n    transition.didSetProps();\n    return null;\n  }\n\n  shouldComponentUpdate() {\n    return false;\n  }\n\n  componentWillUnmount() {\n    this.state.transition.destroy();\n  }\n\n  private onSetRef = (ref: any) => {\n    if (!ref) return;\n    const element: any = ref ? findNodeHandle(ref) : null;\n    const { transition } = this.state;\n    transition.element = element;\n  };\n\n  render() {\n    // console.log(\"RNSharedElementTransitionView.render\");\n    return <View ref={this.onSetRef} />;\n  }\n}\n"]}