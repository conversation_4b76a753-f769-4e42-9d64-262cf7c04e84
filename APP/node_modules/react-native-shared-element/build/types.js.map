{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.tsx"], "names": [], "mappings": "", "sourcesContent": ["export type SharedElementNode = {\n  ref: any;\n  nodeHandle: number;\n  isParent: boolean;\n  parentInstance: any;\n};\n\nexport type SharedElementAnimation = \"move\" | \"fade\" | \"fade-in\" | \"fade-out\";\n\nexport type SharedElementResize = \"auto\" | \"stretch\" | \"clip\" | \"none\";\n\nexport type SharedElementAlign =\n  | \"auto\"\n  | \"left-top\"\n  | \"left-center\"\n  | \"left-bottom\"\n  | \"right-top\"\n  | \"right-center\"\n  | \"right-bottom\"\n  | \"center-top\"\n  | \"center-center\"\n  | \"center-bottom\";\n\nexport type SharedElementNodeType =\n  | \"startNode\"\n  | \"endNode\"\n  | \"startAncestor\"\n  | \"endAncestor\";\n\nexport type SharedElementContentType =\n  | \"none\"\n  | \"snapshotView\"\n  | \"snapshotImage\"\n  | \"image\";\n"]}