{"formatVersion": "1.1", "component": {"group": "host.exp.exponent", "module": "expo.modules.documentpicker", "version": "13.1.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}, {"group": "commons-io", "module": "commons-io", "version": {"requires": "2.6"}}], "files": [{"name": "expo.modules.documentpicker-13.1.5.aar", "url": "expo.modules.documentpicker-13.1.5.aar", "size": 21219, "sha512": "cc92538f05792f089e79863be993771738df2cf39c09cf9694da5a24091d209ab5a51de57525fc60036b14e00cd60faccd70ee440c4d65ae83d585f1aff919fc", "sha256": "42424eabb04455eab143a3278bc7b6be4bb90062f66e123971d412f0b5dac340", "sha1": "b461d0643f29876da6f985c7aba9733bf88a6ba6", "md5": "f3f3b831f7ee92cfc4738a2b33716942"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.0.0"}}, {"group": "commons-io", "module": "commons-io", "version": {"requires": "2.6"}}], "files": [{"name": "expo.modules.documentpicker-13.1.5.aar", "url": "expo.modules.documentpicker-13.1.5.aar", "size": 21219, "sha512": "cc92538f05792f089e79863be993771738df2cf39c09cf9694da5a24091d209ab5a51de57525fc60036b14e00cd60faccd70ee440c4d65ae83d585f1aff919fc", "sha256": "42424eabb04455eab143a3278bc7b6be4bb90062f66e123971d412f0b5dac340", "sha1": "b461d0643f29876da6f985c7aba9733bf88a6ba6", "md5": "f3f3b831f7ee92cfc4738a2b33716942"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.documentpicker-13.1.5-sources.jar", "url": "expo.modules.documentpicker-13.1.5-sources.jar", "size": 4188, "sha512": "5ff342d5af582183d339ebf9259871097897246159fd14e9b324504f552e413deec0a73f4de9bcf764cea56eb74adc2b4904fc4c55a46363dc3491a0dd8b499f", "sha256": "ea8bcf17f70905d5a664af39497d1f47d6c35dbd15fdf1e5686d94caba293616", "sha1": "fea457edea02a2402b77b0567cdf58bf2ec0f121", "md5": "64d7909f101185c8e9e38b3166b24f60"}]}]}