{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["// @needsAudit\nexport type DocumentPickerOptions = {\n  /**\n   * The [MIME type(s)](https://en.wikipedia.org/wiki/Media_type) of the documents that are available\n   * to be picked. It also supports wildcards like `'image/*'` to choose any image. To allow any type\n   * of document you can use `'&ast;/*'`.\n   * @default '&ast;/*'\n   */\n  type?: string | string[];\n  /**\n   * If `true`, the picked file is copied to [`FileSystem.CacheDirectory`](./filesystem#filesystemcachedirectory),\n   * which allows other Expo APIs to read the file immediately. This may impact performance for\n   * large files, so you should consider setting this to `false` if you expect users to pick\n   * particularly large files and your app does not need immediate read access.\n   * @default true\n   */\n  copyToCacheDirectory?: boolean;\n  /**\n   * Allows multiple files to be selected from the system UI.\n   * @default false\n   *\n   */\n  multiple?: boolean;\n  /**\n   * If `true`, asset url is base64 from the file\n   * If `false`, asset url is the file url parameter\n   * @platform web\n   * @default true\n   */\n  base64?: boolean;\n};\n\nexport type DocumentPickerAsset = {\n  /**\n   * Document original name.\n   */\n  name: string;\n  /**\n   * Document size in bytes.\n   */\n  size?: number;\n  /**\n   * An URI to the local document file.\n   */\n  uri: string;\n  /**\n   * Document MIME type.\n   */\n  mimeType?: string;\n  /**\n   * Timestamp of last document modification.\n   */\n  lastModified?: number;\n  /**\n   * `File` object for the parity with web File API.\n   * @platform web\n   */\n  file?: File;\n};\n\n/**\n * Type representing successful and canceled document pick result.\n */\nexport type DocumentPickerResult = DocumentPickerSuccessResult | DocumentPickerCanceledResult;\n\n/**\n * Type representing successful pick result.\n */\nexport type DocumentPickerSuccessResult = {\n  /**\n   * If asset data have been returned this should always be `false`.\n   */\n  canceled: false;\n  /**\n   * An array of picked assets.\n   */\n  assets: DocumentPickerAsset[];\n  /**\n   * `FileList` object for the parity with web File API.\n   * @platform web\n   */\n  output?: FileList;\n};\n\n/**\n * Type representing canceled pick result.\n */\nexport type DocumentPickerCanceledResult = {\n  /**\n   *  Always `true` when the request was canceled.\n   */\n  canceled: true;\n  /**\n   *  Always `null` when the request was canceled.\n   */\n  assets: null;\n  /**\n   * Always `null` when the request was canceled.\n   * @platform web\n   */\n  output?: null;\n};\n"]}