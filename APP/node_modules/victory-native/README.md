[![Victory Native](https://oss.nearform.com/api/banner?badge=victory&text=victory+native&bg=9c2f1e)](https://commerce.nearform.com/open-source/victory-native/)

<div align="center">
  <strong>
    high-performance charts powered by D3, Skia, and Reanimated for React Native.
  </strong>

  <br />
  <br />

  <a href="https://npmjs.com/package/victory-native">
    <img alt="weekly downloads" src="https://img.shields.io/npm/dw/victory-native.svg">
  </a>
  <a href="https://npmjs.com/package/victory-native">
    <img alt="current version" src="https://img.shields.io/npm/v/victory-native.svg">
  </a>
  <a href="https://github.com/FormidableLabs/victory-native/actions">
    <img alt="build status" src="https://github.com/FormidableLabs/victory/actions/workflows/ci.yml/badge.svg">
  </a>

  <a href="https://github.com/FormidableLabs/victory-native-xl#maintenance-status">
    <img alt="Maintenance Status" src="https://img.shields.io/badge/maintenance-active-green.svg" />
  </a>
</div>

<p>&nbsp;</p>

<div align="center">

https://github.com/FormidableLabs/victory-native-xl/assets/12721310/20bada2d-9903-4fe1-9b5f-3b516c0d7428

</div>

## Installation

Start by installing the peer dependencies of `victory-native` – React Native Reanimated, Gesture Handler, and Skia:

```shell
yarn add react-native-reanimated react-native-gesture-handler @shopify/react-native-skia
```

For Reanimated, you'll need to add `"react-native-reanimated/plugin"` to your `plugins` list in your `babel.config.js` config file.

Then install `victory-native`:

```shell
yarn add victory-native
```

To get started and read more about the API, view the [docs here](https://formidable.com/open-source/victory-native).

## LICENSE

MIT

## Maintenance Status

**Active:** Nearform is actively working on this project, and we expect to continue for work for the foreseeable future. Bug reports, feature requests and pull requests are welcome.

[maintenance-image]: https://img.shields.io/badge/maintenance-active-green.svg
