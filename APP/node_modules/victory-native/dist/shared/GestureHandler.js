"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GestureHandler = void 0;
const react_native_gesture_handler_1 = require("react-native-gesture-handler");
const react_native_reanimated_1 = __importStar(require("react-native-reanimated"));
const React = __importStar(require("react"));
const transform_1 = require("../utils/transform");
const GestureHandler = ({ gesture, dimensions, transformState, debug = false, config, }) => {
    const { x, y, width, height } = dimensions;
    const style = (0, react_native_reanimated_1.useAnimatedStyle)(() => {
        // let m4: Matrix4 = identity4;
        let transforms = [];
        if (transformState === null || transformState === void 0 ? void 0 : transformState.matrix.value) {
            const decomposed = (0, transform_1.getTransformComponents)(transformState.matrix.value);
            transforms = [
                { translateX: decomposed.translateX },
                { translateY: decomposed.translateY },
                { scaleX: decomposed.scaleX },
                { scaleY: decomposed.scaleY },
            ];
            // m4 = convertToColumnMajor(transformState.matrix.value);
        }
        return {
            position: "absolute",
            backgroundColor: debug ? "rgba(100, 200, 300, 0.4)" : "transparent",
            // x,
            // y,
            left: x,
            top: y,
            width,
            height,
            transform: [
                { translateX: -width / 2 - x },
                { translateY: -height / 2 },
                // Running into issues using 'matrix' transforms when enabling the new arch:
                // https://github.com/facebook/react-native/issues/47467
                // {
                //   matrix: m4
                //     ? Platform.OS === "web"
                //       ? convertToAffineMatrix(m4)
                //       : undefined
                //     : undefined,
                // },
                ...transforms,
                { translateX: x + width / 2 },
                { translateY: height / 2 },
            ],
        };
    });
    return (<react_native_gesture_handler_1.GestureDetector {...config} gesture={gesture}>
      <react_native_reanimated_1.default.View style={style}/>
    </react_native_gesture_handler_1.GestureDetector>);
};
exports.GestureHandler = GestureHandler;
