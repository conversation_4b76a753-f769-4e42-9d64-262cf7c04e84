import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { tasksService } from '../services/supabase/tasks';
import { getCurrentUser } from '../services/supabase/client';

export interface Task {
  id: string;
  user_id: string;
  group_id: string | null;
  title: string;
  description: string | null;
  status: 'todo' | 'in_progress' | 'done' | 'archived';
  priority: 'low' | 'medium' | 'high';
  column_id: string;
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_photo_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface TaskColumn {
  id: string;
  title: string;
  status: Task['status'];
  order: number;
  color: string;
}

export interface TaskFilter {
  status?: Task['status'][];
  priority?: Task['priority'][];
  assigned_to?: string[];
  due_date?: 'overdue' | 'today' | 'this_week' | 'this_month';
  search?: string;
}

export interface DragState {
  isDragging: boolean;
  draggedTask: Task | null;
  sourceColumn: string | null;
  targetColumn: string | null;
  dragOffset: { x: number; y: number };
}

export interface TasksState {
  // Data
  tasks: Task[];
  columns: TaskColumn[];
  isLoading: boolean;
  error: string | null;

  // View state
  viewMode: 'kanban' | 'table';
  filter: TaskFilter;
  sortBy: 'created_at' | 'updated_at' | 'due_date' | 'priority' | 'title';
  sortOrder: 'asc' | 'desc';

  // Drag and drop state
  dragState: DragState;

  // Selection state
  selectedTasks: string[];

  // Real-time subscription state
  subscription: any;
  isSubscribed: boolean;
  
  // Actions - CRUD
  createTask: (task: Omit<Task, 'id' | 'created_at' | 'updated_at'>) => Promise<Task | null>;
  updateTask: (id: string, updates: Partial<Task>) => Promise<boolean>;
  deleteTask: (id: string) => Promise<boolean>;
  bulkUpdateTasks: (taskIds: string[], updates: Partial<Task>) => Promise<boolean>;
  duplicateTask: (id: string) => Promise<Task | null>;
  
  // Actions - Columns
  createColumn: (column: Omit<TaskColumn, 'id'>) => void;
  updateColumn: (id: string, updates: Partial<TaskColumn>) => void;
  deleteColumn: (id: string) => void;
  reorderColumns: (columnIds: string[]) => void;
  
  // Actions - View Management
  setViewMode: (mode: 'kanban' | 'table') => void;
  setFilter: (filter: Partial<TaskFilter>) => void;
  clearFilter: () => void;
  setSortBy: (sortBy: TasksState['sortBy']) => void;
  setSortOrder: (order: TasksState['sortOrder']) => void;
  
  // Actions - Selection
  selectTask: (taskId: string) => void;
  selectMultipleTasks: (taskIds: string[]) => void;
  deselectTask: (taskId: string) => void;
  clearSelection: () => void;
  toggleTaskSelection: (taskId: string) => void;
  
  // Actions - Drag and Drop
  startDrag: (task: Task, sourceColumn: string, offset: { x: number; y: number }) => void;
  updateDrag: (targetColumn: string | null, offset: { x: number; y: number }) => void;
  endDrag: () => Promise<boolean>;
  cancelDrag: () => void;
  
  // Actions - Data Management
  loadTasks: (groupId?: string) => Promise<void>;
  refreshTasks: () => Promise<void>;
  syncWithServer: () => Promise<void>;
  
  // Actions - Persistence
  persistState: () => Promise<void>;
  loadPersistedState: () => Promise<void>;
  clearPersistedState: () => Promise<void>;

  // Actions - Real-time subscriptions
  subscribeToTasks: (groupId?: string) => Promise<void>;
  unsubscribeFromTasks: () => void;

  // Computed getters
  getFilteredTasks: () => Task[];
  getTasksByColumn: (columnId: string) => Task[];
  getTasksByStatus: (status: Task['status']) => Task[];
  getOverdueTasks: () => Task[];
  getTasksAssignedToUser: (userId: string) => Task[];
  getTaskStats: () => {
    total: number;
    todo: number;
    inProgress: number;
    done: number;
    overdue: number;
  };
}

const TASKS_STORAGE_KEY = 'tasks_state';

// Default columns
const DEFAULT_COLUMNS: TaskColumn[] = [
  { id: 'todo', title: 'To Do', status: 'todo', order: 0, color: '#6750A4' },
  { id: 'in_progress', title: 'In Progress', status: 'in_progress', order: 1, color: '#7C4DFF' },
  { id: 'done', title: 'Done', status: 'done', order: 2, color: '#4CAF50' },
];

// Helper function to generate unique ID
const generateId = () => `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Helper function to sort tasks
const sortTasks = (tasks: Task[], sortBy: string, sortOrder: string): Task[] => {
  return [...tasks].sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    switch (sortBy) {
      case 'title':
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
        break;
      case 'created_at':
        aValue = new Date(a.created_at).getTime();
        bValue = new Date(b.created_at).getTime();
        break;
      case 'updated_at':
        aValue = new Date(a.updated_at).getTime();
        bValue = new Date(b.updated_at).getTime();
        break;
      case 'due_date':
        aValue = a.due_date ? new Date(a.due_date).getTime() : Infinity;
        bValue = b.due_date ? new Date(b.due_date).getTime() : Infinity;
        break;
      case 'priority':
        const priorityOrder = { low: 0, medium: 1, high: 2 };
        aValue = priorityOrder[a.priority];
        bValue = priorityOrder[b.priority];
        break;
      default:
        aValue = a.title.toLowerCase();
        bValue = b.title.toLowerCase();
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

// Helper function to filter tasks
const filterTasks = (tasks: Task[], filter: TaskFilter): Task[] => {
  return tasks.filter(task => {
    // Status filter
    if (filter.status && filter.status.length > 0) {
      if (!filter.status.includes(task.status)) return false;
    }
    
    // Priority filter
    if (filter.priority && filter.priority.length > 0) {
      if (!filter.priority.includes(task.priority)) return false;
    }
    
    // Assigned to filter
    if (filter.assigned_to && filter.assigned_to.length > 0) {
      if (!task.assigned_to || !filter.assigned_to.includes(task.assigned_to)) return false;
    }
    
    // Due date filter
    if (filter.due_date) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const taskDueDate = task.due_date ? new Date(task.due_date) : null;
      
      switch (filter.due_date) {
        case 'overdue':
          if (!taskDueDate || taskDueDate >= today) return false;
          break;
        case 'today':
          if (!taskDueDate || taskDueDate.toDateString() !== today.toDateString()) return false;
          break;
        case 'this_week':
          const weekEnd = new Date(today);
          weekEnd.setDate(today.getDate() + 7);
          if (!taskDueDate || taskDueDate < today || taskDueDate > weekEnd) return false;
          break;
        case 'this_month':
          const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
          if (!taskDueDate || taskDueDate < today || taskDueDate > monthEnd) return false;
          break;
      }
    }
    
    // Search filter
    if (filter.search) {
      const searchLower = filter.search.toLowerCase();
      const titleMatch = task.title.toLowerCase().includes(searchLower);
      const descriptionMatch = task.description?.toLowerCase().includes(searchLower);
      if (!titleMatch && !descriptionMatch) return false;
    }
    
    return true;
  });
};

export const useTasksStore = create<TasksState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    tasks: [],
    columns: DEFAULT_COLUMNS,
    isLoading: false,
    error: null,
    
    viewMode: 'kanban',
    filter: {},
    sortBy: 'created_at',
    sortOrder: 'desc',
    
    dragState: {
      isDragging: false,
      draggedTask: null,
      sourceColumn: null,
      targetColumn: null,
      dragOffset: { x: 0, y: 0 },
    },
    
    selectedTasks: [],

    // Real-time subscription state
    subscription: null,
    isSubscribed: false,
    
    // CRUD Actions
    createTask: async (taskData) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const newTask = await tasksService.createTask(user.id, taskData);

        set(state => ({
          tasks: [...state.tasks, newTask],
          isLoading: false,
        }));

        // Persist changes
        await get().persistState();

        return newTask;
      } catch (error) {
        set({ error: 'Failed to create task', isLoading: false });
        console.error('Error creating task:', error);
        return null;
      }
    },
    
    updateTask: async (id, updates) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const updatedTask = await tasksService.updateTask(id, user.id, updates);

        set(state => ({
          tasks: state.tasks.map(task =>
            task.id === id ? updatedTask : task
          ),
          isLoading: false,
        }));

        // Persist changes
        await get().persistState();

        return true;
      } catch (error) {
        set({ error: 'Failed to update task', isLoading: false });
        console.error('Error updating task:', error);
        return false;
      }
    },
    
    deleteTask: async (id) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const success = await tasksService.deleteTask(id, user.id);

        if (success) {
          set(state => ({
            tasks: state.tasks.filter(task => task.id !== id),
          selectedTasks: state.selectedTasks.filter(taskId => taskId !== id),
            isLoading: false,
          }));

          // Persist changes
          await get().persistState();
        } else {
          set({ isLoading: false });
        }

        return success;
      } catch (error) {
        set({ error: 'Failed to delete task', isLoading: false });
        console.error('Error deleting task:', error);
        return false;
      }
    },
    
    bulkUpdateTasks: async (taskIds, updates) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const updatedTasks = await tasksService.bulkUpdateTasks(taskIds, user.id, updates);

        set(state => ({
          tasks: state.tasks.map(task => {
            const updatedTask = updatedTasks.find(ut => ut.id === task.id);
            return updatedTask || task;
          }),
          isLoading: false,
        }));

        // Persist changes
        await get().persistState();

        return true;
      } catch (error) {
        set({ error: 'Failed to update tasks', isLoading: false });
        console.error('Error bulk updating tasks:', error);
        return false;
      }
    },
    
    duplicateTask: async (id) => {
      try {
        const originalTask = get().tasks.find(task => task.id === id);
        if (!originalTask) return null;
        
        const duplicatedTask = await get().createTask({
          ...originalTask,
          title: `${originalTask.title} (Copy)`,
          id: undefined as any,
          created_at: undefined as any,
          updated_at: undefined as any,
        });
        
        return duplicatedTask;
      } catch (error) {
        console.error('Error duplicating task:', error);
        return null;
      }
    },
    
    // Column Actions
    createColumn: (columnData) => {
      const newColumn: TaskColumn = {
        ...columnData,
        id: generateId(),
      };
      
      set(state => ({
        columns: [...state.columns, newColumn],
      }));
    },
    
    updateColumn: (id, updates) => {
      set(state => ({
        columns: state.columns.map(column => 
          column.id === id ? { ...column, ...updates } : column
        ),
      }));
    },
    
    deleteColumn: (id) => {
      set(state => ({
        columns: state.columns.filter(column => column.id !== id),
        tasks: state.tasks.filter(task => task.column_id !== id),
      }));
    },
    
    reorderColumns: (columnIds) => {
      set(state => ({
        columns: columnIds.map((id, index) => {
          const column = state.columns.find(col => col.id === id);
          return column ? { ...column, order: index } : null;
        }).filter(Boolean) as TaskColumn[],
      }));
    },
    
    // View Management Actions
    setViewMode: (mode) => set({ viewMode: mode }),
    
    setFilter: (newFilter) => {
      set(state => ({
        filter: { ...state.filter, ...newFilter },
      }));
    },
    
    clearFilter: () => set({ filter: {} }),
    
    setSortBy: (sortBy) => set({ sortBy }),
    
    setSortOrder: (order) => set({ sortOrder: order }),
    
    // Selection Actions
    selectTask: (taskId) => {
      set(state => ({
        selectedTasks: state.selectedTasks.includes(taskId) 
          ? state.selectedTasks 
          : [...state.selectedTasks, taskId],
      }));
    },
    
    selectMultipleTasks: (taskIds) => {
      set({ selectedTasks: taskIds });
    },
    
    deselectTask: (taskId) => {
      set(state => ({
        selectedTasks: state.selectedTasks.filter(id => id !== taskId),
      }));
    },
    
    clearSelection: () => set({ selectedTasks: [] }),
    
    toggleTaskSelection: (taskId) => {
      set(state => ({
        selectedTasks: state.selectedTasks.includes(taskId)
          ? state.selectedTasks.filter(id => id !== taskId)
          : [...state.selectedTasks, taskId],
      }));
    },
    
    // Drag and Drop Actions
    startDrag: (task, sourceColumn, offset) => {
      set({
        dragState: {
          isDragging: true,
          draggedTask: task,
          sourceColumn,
          targetColumn: sourceColumn,
          dragOffset: offset,
        },
      });
    },
    
    updateDrag: (targetColumn, offset) => {
      set(state => ({
        dragState: {
          ...state.dragState,
          targetColumn,
          dragOffset: offset,
        },
      }));
    },
    
    endDrag: async () => {
      const { dragState } = get();
      
      if (!dragState.isDragging || !dragState.draggedTask) {
        get().cancelDrag();
        return false;
      }
      
      const { draggedTask, sourceColumn, targetColumn } = dragState;
      
      // Update task if column changed
      if (sourceColumn !== targetColumn && targetColumn) {
        const targetColumnData = get().columns.find(col => col.id === targetColumn);
        if (targetColumnData) {
          await get().updateTask(draggedTask.id, {
            column_id: targetColumn,
            status: targetColumnData.status,
          });
        }
      }
      
      // Clear drag state
      set({
        dragState: {
          isDragging: false,
          draggedTask: null,
          sourceColumn: null,
          targetColumn: null,
          dragOffset: { x: 0, y: 0 },
        },
      });
      
      return true;
    },
    
    cancelDrag: () => {
      set({
        dragState: {
          isDragging: false,
          draggedTask: null,
          sourceColumn: null,
          targetColumn: null,
          dragOffset: { x: 0, y: 0 },
        },
      });
    },
    
    // Data Management Actions
    loadTasks: async (groupId) => {
      try {
        set({ isLoading: true, error: null });

        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        const tasks = await tasksService.getUserTasks(user.id, groupId);

        set({
          tasks,
          isLoading: false
        });

        // Persist changes
        await get().persistState();
      } catch (error) {
        set({ error: 'Failed to load tasks', isLoading: false });
        console.error('Error loading tasks:', error);
      }
    },
    
    refreshTasks: async () => {
      await get().loadTasks();
    },
    
    syncWithServer: async () => {
      try {
        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        // Reload tasks from server
        await get().loadTasks();
        console.log('Tasks synced with server');
      } catch (error) {
        console.error('Failed to sync with server:', error);
      }
    },
    
    // Persistence Actions
    persistState: async () => {
      try {
        const state = get();
        const stateToPersist = {
          tasks: state.tasks,
          columns: state.columns,
          viewMode: state.viewMode,
          filter: state.filter,
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
        };
        
        await AsyncStorage.setItem(TASKS_STORAGE_KEY, JSON.stringify(stateToPersist));
      } catch (error) {
        console.error('Failed to persist tasks state:', error);
      }
    },
    
    loadPersistedState: async () => {
      try {
        const stored = await AsyncStorage.getItem(TASKS_STORAGE_KEY);
        if (stored) {
          const parsedState = JSON.parse(stored);
          set(state => ({
            ...state,
            ...parsedState,
            isLoading: false,
            error: null,
          }));
        }
      } catch (error) {
        console.error('Failed to load persisted tasks state:', error);
      }
    },
    
    clearPersistedState: async () => {
      try {
        await AsyncStorage.removeItem(TASKS_STORAGE_KEY);
        set({
          tasks: [],
          columns: DEFAULT_COLUMNS,
          viewMode: 'kanban',
          filter: {},
          sortBy: 'created_at',
          sortOrder: 'desc',
          selectedTasks: [],
        });
      } catch (error) {
        console.error('Failed to clear persisted tasks state:', error);
      }
    },

    // Real-time subscription methods
    subscribeToTasks: async (groupId) => {
      try {
        const user = await getCurrentUser();
        if (!user) {
          throw new Error('User not authenticated');
        }

        // Unsubscribe from existing subscription
        get().unsubscribeFromTasks();

        const subscription = tasksService.subscribeToTasks(
          user.id,
          (payload) => {
            const { eventType, new: newRecord, old: oldRecord } = payload;

            set(state => {
              let updatedTasks = [...state.tasks];

              switch (eventType) {
                case 'INSERT':
                  if (newRecord) {
                    // Map the database row to Task interface manually
                    const newTask: Task = {
                      id: newRecord.id,
                      user_id: newRecord.user_id,
                      group_id: newRecord.group_id,
                      title: newRecord.title,
                      description: newRecord.description,
                      status: newRecord.status,
                      priority: newRecord.priority,
                      column_id: newRecord.column_id,
                      due_date: newRecord.due_date,
                      assigned_to: newRecord.assigned_to,
                      assigned_to_photo_url: newRecord.assigned_to_photo_url,
                      created_at: newRecord.created_at,
                      updated_at: newRecord.updated_at,
                    };
                    updatedTasks = [...updatedTasks, newTask];
                  }
                  break;

                case 'UPDATE':
                  if (newRecord) {
                    // Map the database row to Task interface manually
                    const updatedTask: Task = {
                      id: newRecord.id,
                      user_id: newRecord.user_id,
                      group_id: newRecord.group_id,
                      title: newRecord.title,
                      description: newRecord.description,
                      status: newRecord.status,
                      priority: newRecord.priority,
                      column_id: newRecord.column_id,
                      due_date: newRecord.due_date,
                      assigned_to: newRecord.assigned_to,
                      assigned_to_photo_url: newRecord.assigned_to_photo_url,
                      created_at: newRecord.created_at,
                      updated_at: newRecord.updated_at,
                    };
                    updatedTasks = updatedTasks.map(task =>
                      task.id === updatedTask.id ? updatedTask : task
                    );
                  }
                  break;

                case 'DELETE':
                  if (oldRecord) {
                    updatedTasks = updatedTasks.filter(task => task.id !== oldRecord.id);
                  }
                  break;
              }

              return { tasks: updatedTasks };
            });
          },
          groupId
        );

        set({
          subscription,
          isSubscribed: true
        });
      } catch (error) {
        console.error('Failed to subscribe to tasks:', error);
        set({ error: 'Failed to subscribe to real-time updates' });
      }
    },

    unsubscribeFromTasks: () => {
      const state = get();
      if (state.subscription) {
        tasksService.unsubscribeFromTasks(state.subscription);
        set({
          subscription: null,
          isSubscribed: false
        });
      }
    },

    // Computed getters
    getFilteredTasks: () => {
      const state = get();
      const filtered = filterTasks(state.tasks, state.filter);
      return sortTasks(filtered, state.sortBy, state.sortOrder);
    },
    
    getTasksByColumn: (columnId) => {
      const state = get();
      return state.getFilteredTasks().filter(task => task.column_id === columnId);
    },
    
    getTasksByStatus: (status) => {
      const state = get();
      return state.getFilteredTasks().filter(task => task.status === status);
    },
    
    getOverdueTasks: () => {
      const state = get();
      const now = new Date();
      return state.getFilteredTasks().filter(task => {
        if (!task.due_date) return false;
        return new Date(task.due_date) < now && task.status !== 'done';
      });
    },
    
    getTasksAssignedToUser: (userId) => {
      const state = get();
      return state.getFilteredTasks().filter(task => task.assigned_to === userId);
    },
    
    getTaskStats: () => {
      const state = get();
      const tasks = state.getFilteredTasks();
      const now = new Date();
      
      return {
        total: tasks.length,
        todo: tasks.filter(task => task.status === 'todo').length,
        inProgress: tasks.filter(task => task.status === 'in_progress').length,
        done: tasks.filter(task => task.status === 'done').length,
        overdue: tasks.filter(task => {
          if (!task.due_date) return false;
          return new Date(task.due_date) < now && task.status !== 'done';
        }).length,
      };
    },
  }))
);
