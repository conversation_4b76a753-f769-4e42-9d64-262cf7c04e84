import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { VictoryPie } from 'victory-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { GoalProgress as GoalProgressType } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface GoalProgressProps {
  goalProgress: GoalProgressType;
  showAnimation?: boolean;
  onEditGoals?: () => void;
}

interface CircularProgressProps {
  percentage: number;
  size: number;
  strokeWidth: number;
  color: string;
  backgroundColor: string;
  current: number;
  target: number;
  label: string;
  unit: string;
  showAnimation?: boolean;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  size,
  strokeWidth,
  color,
  backgroundColor,
  current,
  target,
  label,
  unit,
  showAnimation = true,
}) => {
  const theme = useDynamicTheme();
  const animatedValue = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  useEffect(() => {
    if (showAnimation) {
      Animated.parallel([
        Animated.timing(animatedValue, {
          toValue: percentage,
          duration: 1500,
          useNativeDriver: false,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      animatedValue.setValue(percentage);
      scaleAnim.setValue(1);
    }
  }, [percentage, showAnimation]);

  const formatValue = (value: number): string => {
    if (unit === 'hours') {
      const hours = Math.floor(value / 3600);
      const minutes = Math.floor((value % 3600) / 60);
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      }
      return `${minutes}m`;
    }
    return value.toString();
  };

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    progressContainer: {
      position: 'relative',
      alignItems: 'center',
      justifyContent: 'center',
    },
    centerContent: {
      position: 'absolute',
      alignItems: 'center',
      justifyContent: 'center',
    },
    currentValue: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.onSurface,
      fontWeight: '700',
      textAlign: 'center',
    },
    targetValue: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 2,
    },
    percentage: {
      ...ExpressiveTypography.labelMedium,
      color,
      fontWeight: '600',
      textAlign: 'center',
      marginTop: 2,
    },
    label: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginTop: 12,
      fontWeight: '500',
    },
  });

  return (
    <Animated.View style={[styles.container, { transform: [{ scale: scaleAnim }] }]}>
      <View style={styles.progressContainer}>
        <VictoryPie
          data={[
            { x: 'progress', y: percentage },
            { x: 'remaining', y: 100 - percentage },
          ]}
          width={size}
          height={size}
          innerRadius={radius - strokeWidth / 2}
          colorScale={[color, backgroundColor]}
          startAngle={-90}
          endAngle={270}
          labelComponent={<></>}
          animate={{
            duration: showAnimation ? 1500 : 0,
          }}
        />
        
        <View style={styles.centerContent}>
          <Text style={styles.currentValue}>
            {formatValue(current)}
          </Text>
          <Text style={styles.targetValue}>
            of {formatValue(target)}
          </Text>
          <Text style={styles.percentage}>
            {Math.round(percentage)}%
          </Text>
        </View>
      </View>
      
      <Text style={styles.label}>{label}</Text>
    </Animated.View>
  );
};

export const GoalProgress: React.FC<GoalProgressProps> = ({
  goalProgress,
  showAnimation = true,
  onEditGoals,
}) => {
  const theme = useDynamicTheme();

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
    },
    editButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.primaryContainer,
    },
    editButtonText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onPrimaryContainer,
      fontWeight: '600',
    },
    progressGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 24,
      flexWrap: 'wrap',
      gap: 16,
    },
    progressItem: {
      alignItems: 'center',
      minWidth: (screenWidth - 64) / 3,
    },
    summaryContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
      backgroundColor: theme.colors.surfaceContainer,
    },
    summaryTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 12,
      textAlign: 'center',
    },
    summaryGrid: {
      flexDirection: 'row',
      justifyContent: 'space-around',
    },
    summaryItem: {
      alignItems: 'center',
    },
    summaryValue: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.primary,
      fontWeight: '600',
    },
    summaryLabel: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    motivationContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: theme.colors.primaryContainer,
    },
    motivationText: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onPrimaryContainer,
      textAlign: 'center',
      lineHeight: 20,
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 16,
    },
    setupButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 20,
    },
    setupButtonText: {
      ...ExpressiveTypography.labelLarge,
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
  });

  // Get motivation message based on progress
  const getMotivationMessage = (): string => {
    const dailyProgress = goalProgress.daily.percentage;
    const weeklyProgress = goalProgress.weekly.percentage;
    
    if (dailyProgress >= 100) {
      return "🎉 Daily goal achieved! You're crushing it today!";
    } else if (dailyProgress >= 80) {
      return "🔥 Almost there! Just a little more to reach your daily goal!";
    } else if (dailyProgress >= 50) {
      return "💪 Great progress! You're halfway to your daily goal!";
    } else if (weeklyProgress >= 100) {
      return "⭐ Weekly goal completed! Keep up the amazing work!";
    } else if (weeklyProgress >= 80) {
      return "🚀 Excellent weekly progress! You're on track for success!";
    } else {
      return "📚 Every minute counts! Start building your study momentum!";
    }
  };

  // Calculate overall progress
  const calculateOverallProgress = (): number => {
    return (goalProgress.daily.percentage + goalProgress.weekly.percentage + goalProgress.monthly.percentage) / 3;
  };

  // Check if goals are set
  const hasGoals = goalProgress.daily.target > 0 || goalProgress.weekly.target > 0 || goalProgress.monthly.target > 0;

  if (!hasGoals) {
    return (
      <ExpressiveCard style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Goal Progress</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            Set your study goals to track your progress and stay motivated!
          </Text>
          <TouchableOpacity style={styles.setupButton} onPress={onEditGoals}>
            <Text style={styles.setupButtonText}>Setup Goals</Text>
          </TouchableOpacity>
        </View>
      </ExpressiveCard>
    );
  }

  return (
    <ExpressiveCard style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Goal Progress</Text>
        {onEditGoals && (
          <TouchableOpacity style={styles.editButton} onPress={onEditGoals}>
            <Text style={styles.editButtonText}>Edit Goals</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Progress Circles */}
      <View style={styles.progressGrid}>
        {goalProgress.daily.target > 0 && (
          <View style={styles.progressItem}>
            <CircularProgress
              percentage={Math.min(goalProgress.daily.percentage, 100)}
              size={100}
              strokeWidth={8}
              color={theme.colors.primary}
              backgroundColor={theme.colors.surfaceVariant}
              current={goalProgress.daily.current}
              target={goalProgress.daily.target}
              label="Daily"
              unit="hours"
              showAnimation={showAnimation}
            />
          </View>
        )}

        {goalProgress.weekly.target > 0 && (
          <View style={styles.progressItem}>
            <CircularProgress
              percentage={Math.min(goalProgress.weekly.percentage, 100)}
              size={100}
              strokeWidth={8}
              color={theme.colors.secondary}
              backgroundColor={theme.colors.surfaceVariant}
              current={goalProgress.weekly.current}
              target={goalProgress.weekly.target}
              label="Weekly"
              unit="hours"
              showAnimation={showAnimation}
            />
          </View>
        )}

        {goalProgress.monthly.target > 0 && (
          <View style={styles.progressItem}>
            <CircularProgress
              percentage={Math.min(goalProgress.monthly.percentage, 100)}
              size={100}
              strokeWidth={8}
              color={theme.colors.tertiary}
              backgroundColor={theme.colors.surfaceVariant}
              current={goalProgress.monthly.current}
              target={goalProgress.monthly.target}
              label="Monthly"
              unit="hours"
              showAnimation={showAnimation}
            />
          </View>
        )}
      </View>

      {/* Summary */}
      <View style={styles.summaryContainer}>
        <Text style={styles.summaryTitle}>Progress Summary</Text>
        <View style={styles.summaryGrid}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>
              {Math.round(calculateOverallProgress())}%
            </Text>
            <Text style={styles.summaryLabel}>Overall</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>
              {Math.round(goalProgress.daily.percentage)}%
            </Text>
            <Text style={styles.summaryLabel}>Today</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryValue}>
              {Math.round(goalProgress.weekly.percentage)}%
            </Text>
            <Text style={styles.summaryLabel}>This Week</Text>
          </View>
        </View>
      </View>

      {/* Motivation */}
      <View style={styles.motivationContainer}>
        <Text style={styles.motivationText}>
          {getMotivationMessage()}
        </Text>
      </View>
    </ExpressiveCard>
  );
};

export default GoalProgress;
