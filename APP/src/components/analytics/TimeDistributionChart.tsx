import React, { useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import {
  Victory<PERSON>ie,
  VictoryBar,
  VictoryChart,
  VictoryAxis,
  VictoryTheme,
  VictoryLabel,
  VictoryTooltip,
  VictoryContainer,
} from 'victory-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { TimeDistribution } from '../../stores/analyticsStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';

const { width: screenWidth } = Dimensions.get('window');

interface TimeDistributionChartProps {
  data: TimeDistribution[];
  chartType?: 'pie' | 'bar';
  showLegend?: boolean;
  showPercentages?: boolean;
  onSubjectPress?: (subject: string) => void;
}

export const TimeDistributionChart: React.FC<TimeDistributionChartProps> = ({
  data,
  chartType = 'pie',
  showLegend = true,
  showPercentages = true,
  onSubjectPress,
}) => {
  const theme = useDynamicTheme();
  const [selectedSubject, setSelectedSubject] = useState<string | null>(null);

  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.titleMedium,
      color: theme.colors.onSurface,
    },
    chartTypeToggle: {
      flexDirection: 'row',
      backgroundColor: theme.colors.surfaceContainer,
      borderRadius: 16,
      padding: 2,
    },
    toggleButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 14,
      minWidth: 50,
      alignItems: 'center',
    },
    activeToggleButton: {
      backgroundColor: theme.colors.primary,
    },
    toggleButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    activeToggleButtonText: {
      color: theme.colors.onPrimary,
      fontWeight: '600',
    },
    chartContainer: {
      alignItems: 'center',
      paddingVertical: 16,
    },
    pieChartContainer: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    barChartContainer: {
      paddingHorizontal: 16,
    },
    legend: {
      paddingHorizontal: 16,
      paddingBottom: 16,
    },
    legendTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 12,
    },
    legendItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
      marginBottom: 4,
    },
    selectedLegendItem: {
      backgroundColor: theme.colors.primaryContainer,
    },
    legendColor: {
      width: 16,
      height: 16,
      borderRadius: 8,
      marginRight: 12,
    },
    legendText: {
      flex: 1,
    },
    legendSubject: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurface,
      fontWeight: '500',
    },
    legendStats: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    legendPercentage: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.primary,
      fontWeight: '600',
      minWidth: 50,
      textAlign: 'right',
    },
    emptyState: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 48,
      paddingHorizontal: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
    },
    totalTime: {
      alignItems: 'center',
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
      marginTop: 16,
    },
    totalTimeLabel: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 4,
    },
    totalTimeValue: {
      ...ExpressiveTypography.headlineSmall,
      color: theme.colors.primary,
      fontWeight: '600',
    },
  });

  // Format time duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Calculate total time
  const totalTime = useMemo(() => {
    return data.reduce((sum, item) => sum + item.time, 0);
  }, [data]);

  // Prepare chart data
  const chartData = useMemo(() => {
    return data.map((item, index) => ({
      x: item.subject,
      y: item.time,
      label: showPercentages ? `${item.percentage.toFixed(1)}%` : formatDuration(item.time),
      fill: item.color,
      sessions: item.sessions,
    }));
  }, [data, showPercentages]);

  // Handle subject selection
  const handleSubjectPress = (subject: string) => {
    setSelectedSubject(selectedSubject === subject ? null : subject);
    if (onSubjectPress) {
      onSubjectPress(subject);
    }
  };

  // Render empty state
  if (data.length === 0) {
    return (
      <ExpressiveCard style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Time Distribution</Text>
        </View>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>
            No study data available for the selected time period.
          </Text>
        </View>
      </ExpressiveCard>
    );
  }

  // Render pie chart
  const renderPieChart = () => (
    <View style={styles.pieChartContainer}>
      <VictoryPie
        data={chartData}
        width={screenWidth - 32}
        height={280}
        innerRadius={60}
        padAngle={2}
        labelRadius={({ innerRadius }) => (innerRadius as number) + 40}
        labelComponent={
          <VictoryLabel
            style={{
              fontSize: 12,
              fontWeight: '600',
              fill: theme.colors.onSurface,
            }}
          />
        }
        colorScale={data.map(item => item.color)}
        animate={{
          duration: 1000,
          onLoad: { duration: 500 },
        }}
        events={[
          {
            target: 'data',
            eventHandlers: {
              onPress: () => {
                return [
                  {
                    target: 'data',
                    mutation: (props) => {
                      const subject = props.datum.x;
                      handleSubjectPress(subject);
                      return null;
                    },
                  },
                ];
              },
            },
          },
        ]}
      />
    </View>
  );

  // Render bar chart
  const renderBarChart = () => (
    <View style={styles.barChartContainer}>
      <VictoryChart
        theme={VictoryTheme.material}
        width={screenWidth - 32}
        height={280}
        domainPadding={{ x: 20 }}
        padding={{ left: 80, top: 20, right: 40, bottom: 60 }}
        containerComponent={<VictoryContainer responsive={false} />}
      >
        <VictoryAxis
          dependentAxis
          tickFormat={(t) => formatDuration(t)}
          style={{
            tickLabels: {
              fontSize: 10,
              fill: theme.colors.onSurfaceVariant,
            },
            grid: {
              stroke: theme.colors.outline,
              strokeWidth: 0.5,
            },
          }}
        />
        <VictoryAxis
          style={{
            tickLabels: {
              fontSize: 10,
              fill: theme.colors.onSurfaceVariant,
              angle: -45,
            },
          }}
        />
        <VictoryBar
          data={chartData}
          x="x"
          y="y"
          style={{
            data: {
              fill: ({ datum }) => datum.fill,
              fillOpacity: ({ datum }) => 
                selectedSubject === null || selectedSubject === datum.x ? 1 : 0.3,
            },
          }}
          animate={{
            duration: 1000,
            onLoad: { duration: 500 },
          }}
          labelComponent={<VictoryTooltip />}
          events={[
            {
              target: 'data',
              eventHandlers: {
                onPress: () => {
                  return [
                    {
                      target: 'data',
                      mutation: (props) => {
                        const subject = props.datum.x;
                        handleSubjectPress(subject);
                        return null;
                      },
                    },
                  ];
                },
              },
            },
          ]}
        />
      </VictoryChart>
    </View>
  );

  // Render legend
  const renderLegend = () => (
    <View style={styles.legend}>
      <Text style={styles.legendTitle}>Subjects</Text>
      <ScrollView showsVerticalScrollIndicator={false}>
        {data.map((item) => (
          <TouchableOpacity
            key={item.subject}
            style={[
              styles.legendItem,
              selectedSubject === item.subject && styles.selectedLegendItem,
            ]}
            onPress={() => handleSubjectPress(item.subject)}
            activeOpacity={0.7}
          >
            <View style={[styles.legendColor, { backgroundColor: item.color }]} />
            <View style={styles.legendText}>
              <Text style={styles.legendSubject}>{item.subject}</Text>
              <Text style={styles.legendStats}>
                {formatDuration(item.time)} • {item.sessions} session{item.sessions !== 1 ? 's' : ''}
              </Text>
            </View>
            <Text style={styles.legendPercentage}>
              {item.percentage.toFixed(1)}%
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  return (
    <ExpressiveCard style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Time Distribution</Text>
        <View style={styles.chartTypeToggle}>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              chartType === 'pie' && styles.activeToggleButton,
            ]}
            onPress={() => {/* Chart type toggle handled by parent */}}
          >
            <Text
              style={[
                styles.toggleButtonText,
                chartType === 'pie' && styles.activeToggleButtonText,
              ]}
            >
              Pie
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.toggleButton,
              chartType === 'bar' && styles.activeToggleButton,
            ]}
            onPress={() => {/* Chart type toggle handled by parent */}}
          >
            <Text
              style={[
                styles.toggleButtonText,
                chartType === 'bar' && styles.activeToggleButtonText,
              ]}
            >
              Bar
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Chart */}
      <View style={styles.chartContainer}>
        {chartType === 'pie' ? renderPieChart() : renderBarChart()}
      </View>

      {/* Legend */}
      {showLegend && renderLegend()}

      {/* Total Time */}
      <View style={styles.totalTime}>
        <Text style={styles.totalTimeLabel}>Total Study Time</Text>
        <Text style={styles.totalTimeValue}>{formatDuration(totalTime)}</Text>
      </View>
    </ExpressiveCard>
  );
};

export default TimeDistributionChart;
