import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  Vibration,
  Dimensions,
} from 'react-native';
import { Text, IconButton, Surface } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
  interpolate,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { useTimerStore, TimerStatus, TimerMode } from '../../stores/timerStore';

const { width: screenWidth } = Dimensions.get('window');

interface ExpressiveTimerControlsProps {
  onStart?: () => void;
  onPause?: () => void;
  onStop?: () => void;
  onReset?: () => void;
  onModeToggle?: () => void;
  disabled?: boolean;
  compact?: boolean;
}

const ExpressiveTimerControls: React.FC<ExpressiveTimerControlsProps> = ({
  onStart,
  onPause,
  onStop,
  onReset,
  onModeToggle,
  disabled = false,
  compact = false,
}) => {
  const theme = useDynamicTheme();
  const { status, mode, selectedSubject, selectedSubjectColor, vibrationEnabled } = useTimerStore();

  // Animation values
  const primaryButtonScale = useSharedValue(1);
  const primaryButtonRotation = useSharedValue(0);
  const secondaryButtonsOpacity = useSharedValue(1);
  const modeToggleScale = useSharedValue(1);
  const controlsContainerScale = useSharedValue(1);

  // Haptic feedback helper
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'medium') => {
    if (vibrationEnabled) {
      switch (type) {
        case 'light':
          Vibration.vibrate(50);
          break;
        case 'medium':
          Vibration.vibrate(100);
          break;
        case 'heavy':
          Vibration.vibrate([0, 100, 50, 100]);
          break;
      }
    }
  };

  // Status-based animations
  useEffect(() => {
    switch (status) {
      case 'running':
        // Subtle pulsing for running state
        primaryButtonRotation.value = withTiming(0, { duration: 300 });
        secondaryButtonsOpacity.value = withTiming(1, { duration: 300 });
        break;
        
      case 'paused':
        // Gentle breathing animation for paused state
        primaryButtonScale.value = withSequence(
          withTiming(1.05, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
          withTiming(1, { duration: 1000, easing: Easing.inOut(Easing.ease) })
        );
        break;
        
      case 'completed':
        // Celebration animation
        primaryButtonRotation.value = withSequence(
          withTiming(360, { duration: 500 }),
          withTiming(0, { duration: 0 })
        );
        controlsContainerScale.value = withSequence(
          withSpring(1.1, { damping: 10 }),
          withSpring(1, { damping: 15 })
        );
        runOnJS(triggerHaptic)('heavy');
        break;
        
      default:
        // Idle state
        primaryButtonScale.value = withTiming(1, { duration: 300 });
        primaryButtonRotation.value = withTiming(0, { duration: 300 });
        secondaryButtonsOpacity.value = withTiming(0.7, { duration: 300 });
        break;
    }
  }, [status]);

  // Button press animations
  const createPressAnimation = (sharedValue: Animated.SharedValue<number>) => ({
    onPressIn: () => {
      sharedValue.value = withSpring(0.9, { damping: 15 });
      runOnJS(triggerHaptic)('light');
    },
    onPressOut: () => {
      sharedValue.value = withSpring(1, { damping: 15 });
    },
  });

  // Animated styles
  const primaryButtonAnimatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: primaryButtonScale.value },
      { rotate: `${primaryButtonRotation.value}deg` },
    ],
  }));

  const secondaryButtonsAnimatedStyle = useAnimatedStyle(() => ({
    opacity: secondaryButtonsOpacity.value,
  }));

  const modeToggleAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modeToggleScale.value }],
  }));

  const controlsContainerAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: controlsContainerScale.value }],
  }));

  // Control handlers with animations
  const handleStart = () => {
    runOnJS(triggerHaptic)('medium');
    onStart?.();
  };

  const handlePause = () => {
    runOnJS(triggerHaptic)('medium');
    onPause?.();
  };

  const handleStop = () => {
    runOnJS(triggerHaptic)('heavy');
    onStop?.();
  };

  const handleReset = () => {
    runOnJS(triggerHaptic)('heavy');
    onReset?.();
  };

  const handleModeToggle = () => {
    modeToggleScale.value = withSequence(
      withSpring(0.8, { damping: 15 }),
      withSpring(1.2, { damping: 15 }),
      withSpring(1, { damping: 15 })
    );
    runOnJS(triggerHaptic)('medium');
    onModeToggle?.();
  };

  // Get primary button config
  const getPrimaryButtonConfig = () => {
    const subjectColor = selectedSubjectColor || theme.colors.primary;

    switch (status) {
      case 'idle':
        return {
          icon: 'play',
          label: 'Start',
          onPress: handleStart,
          disabled: disabled || !selectedSubject,
          color: subjectColor,
          backgroundColor: `${subjectColor}20`,
        };
      case 'running':
        return {
          icon: 'pause',
          label: 'Pause',
          onPress: handlePause,
          disabled: disabled,
          color: theme.colors.secondary,
          backgroundColor: theme.colors.secondaryContainer,
        };
      case 'paused':
        return {
          icon: 'play',
          label: 'Resume',
          onPress: handleStart,
          disabled: disabled,
          color: subjectColor,
          backgroundColor: `${subjectColor}20`,
        };
      case 'completed':
        return {
          icon: 'check',
          label: 'Complete',
          onPress: handleReset,
          disabled: disabled,
          color: theme.colors.tertiary,
          backgroundColor: theme.colors.tertiaryContainer,
        };
      default:
        return {
          icon: 'play',
          label: 'Start',
          onPress: handleStart,
          disabled: true,
          color: theme.colors.outline,
          backgroundColor: theme.colors.surfaceVariant,
        };
    }
  };

  const primaryButton = getPrimaryButtonConfig();

  const styles = StyleSheet.create({
    container: {
      alignItems: 'center',
      paddingVertical: compact ? 16 : 24,
    },
    primaryButtonContainer: {
      alignItems: 'center',
      marginBottom: compact ? 16 : 24,
    },
    primaryButton: {
      width: compact ? 80 : 100,
      height: compact ? 80 : 100,
      borderRadius: compact ? 40 : 50,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 8,
      shadowColor: primaryButton.color,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    primaryButtonLabel: {
      fontSize: compact ? 12 : 14,
      fontWeight: '600',
      color: primaryButton.color,
      marginTop: 8,
    },
    secondaryControls: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: compact ? 12 : 16,
    },
    secondaryButton: {
      width: compact ? 48 : 56,
      height: compact ? 48 : 56,
      borderRadius: compact ? 24 : 28,
      backgroundColor: theme.colors.surfaceVariant,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 2,
    },
    modeToggle: {
      position: 'absolute',
      top: compact ? -40 : -48,
      right: 0,
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.secondaryContainer,
      elevation: 4,
    },
    modeToggleText: {
      fontSize: compact ? 10 : 12,
      fontWeight: '600',
      color: theme.colors.onSecondaryContainer,
      textTransform: 'uppercase',
    },
    disabledOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.3)',
      borderRadius: compact ? 40 : 50,
    },
    subjectWarning: {
      marginTop: 8,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      backgroundColor: theme.colors.errorContainer,
    },
    subjectWarningText: {
      fontSize: 12,
      color: theme.colors.onErrorContainer,
      textAlign: 'center',
    },
  });

  return (
    <Animated.View style={[styles.container, controlsContainerAnimatedStyle]}>
      {/* Mode Toggle */}
      {onModeToggle && status === 'idle' && (
        <Animated.View style={[styles.modeToggle, modeToggleAnimatedStyle]}>
          <Pressable onPress={handleModeToggle}>
            <Text style={styles.modeToggleText}>{mode}</Text>
          </Pressable>
        </Animated.View>
      )}

      {/* Primary Control Button */}
      <View style={styles.primaryButtonContainer}>
        <Animated.View style={primaryButtonAnimatedStyle}>
          <Pressable
            style={[
              styles.primaryButton,
              { backgroundColor: primaryButton.backgroundColor },
            ]}
            onPress={primaryButton.onPress}
            disabled={primaryButton.disabled}
            {...createPressAnimation(primaryButtonScale)}
          >
            <IconButton
              icon={primaryButton.icon}
              size={compact ? 32 : 40}
              iconColor={primaryButton.color}
              style={{ margin: 0 }}
            />
            {primaryButton.disabled && <View style={styles.disabledOverlay} />}
          </Pressable>
        </Animated.View>
        <Text style={styles.primaryButtonLabel}>{primaryButton.label}</Text>
      </View>

      {/* Secondary Controls */}
      {(status === 'running' || status === 'paused') && (
        <Animated.View style={[styles.secondaryControls, secondaryButtonsAnimatedStyle]}>
          {status === 'paused' && onStop && (
            <Pressable
              style={styles.secondaryButton}
              onPress={handleStop}
              disabled={disabled}
            >
              <IconButton
                icon="stop"
                size={compact ? 20 : 24}
                iconColor={theme.colors.onSurfaceVariant}
                style={{ margin: 0 }}
              />
            </Pressable>
          )}
          
          {onReset && (
            <Pressable
              style={styles.secondaryButton}
              onPress={handleReset}
              disabled={disabled}
            >
              <IconButton
                icon="restart"
                size={compact ? 20 : 24}
                iconColor={theme.colors.onSurfaceVariant}
                style={{ margin: 0 }}
              />
            </Pressable>
          )}
        </Animated.View>
      )}

      {/* Subject Warning */}
      {status === 'idle' && !selectedSubject && (
        <Surface style={styles.subjectWarning}>
          <Text style={styles.subjectWarningText}>
            Please select a subject to start studying
          </Text>
        </Surface>
      )}
    </Animated.View>
  );
};

export default ExpressiveTimerControls;
