import React, { useCallback, useMemo, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Dimensions,
} from 'react-native';
import { useDynamicTheme } from '../../contexts/DynamicThemeProvider';
import { ExpressiveTypography } from '../../constants/expressiveTheme';
import { useTasksStore, Task } from '../../stores/tasksStore';
import ExpressiveCard from '../surfaces/ExpressiveCard';
import ExpressiveButton from '../buttons/ExpressiveButton';

const { width: screenWidth } = Dimensions.get('window');

interface TaskTableProps {
  groupId?: string | undefined;
  onTaskPress?: (task: Task) => void;
  onCreateTask?: () => void;
  refreshing?: boolean;
  onRefresh?: () => void;
}

export const TaskTable: React.FC<TaskTableProps> = ({
  groupId,
  onTaskPress,
  onCreateTask,
  refreshing = false,
  onRefresh,
}) => {
  const theme = useDynamicTheme();
  const {
    tasks,
    isLoading,
    error,
    filter,
    sortBy,
    sortOrder,
    getFilteredTasks,
    setFilter,
    setSortBy,
    setSortOrder,
    loadTasks,
  } = useTasksStore();

  const [selectedTasks, setSelectedTasks] = useState<string[]>([]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.surface,
    },
    header: {
      padding: 16,
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    title: {
      ...ExpressiveTypography.headlineMedium,
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    subtitle: {
      ...ExpressiveTypography.bodyMedium,
      color: theme.colors.onSurfaceVariant,
    },
    filterContainer: {
      flexDirection: 'row',
      padding: 16,
      gap: 8,
      backgroundColor: theme.colors.surfaceContainer,
    },
    filterButton: {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      backgroundColor: theme.colors.surfaceVariant,
    },
    activeFilterButton: {
      backgroundColor: theme.colors.primary,
    },
    filterButtonText: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    activeFilterButtonText: {
      color: theme.colors.onPrimary,
    },
    sortContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: theme.colors.surfaceContainer,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    sortButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    sortText: {
      ...ExpressiveTypography.labelMedium,
      color: theme.colors.onSurface,
    },
    activeSortText: {
      color: theme.colors.primary,
      fontWeight: '600',
    },
    listContainer: {
      flex: 1,
    },
    taskRow: {
      backgroundColor: theme.colors.surface,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.outline,
    },
    selectedTaskRow: {
      backgroundColor: theme.colors.primaryContainer,
    },
    taskContent: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 16,
    },
    checkbox: {
      width: 20,
      height: 20,
      borderRadius: 4,
      borderWidth: 2,
      borderColor: theme.colors.outline,
      marginRight: 12,
      justifyContent: 'center',
      alignItems: 'center',
    },
    checkedCheckbox: {
      backgroundColor: theme.colors.primary,
      borderColor: theme.colors.primary,
    },
    checkmark: {
      color: theme.colors.onPrimary,
      fontSize: 12,
      fontWeight: 'bold',
    },
    taskInfo: {
      flex: 1,
    },
    taskTitle: {
      ...ExpressiveTypography.titleSmall,
      color: theme.colors.onSurface,
      marginBottom: 2,
    },
    taskDescription: {
      ...ExpressiveTypography.bodySmall,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 4,
    },
    taskMeta: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    statusBadge: {
      paddingHorizontal: 8,
      paddingVertical: 2,
      borderRadius: 12,
    },
    statusText: {
      ...ExpressiveTypography.labelSmall,
      fontWeight: '600',
    },
    priorityBadge: {
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 8,
    },
    priorityText: {
      ...ExpressiveTypography.labelSmall,
      fontSize: 10,
      fontWeight: '600',
    },
    dueDate: {
      ...ExpressiveTypography.labelSmall,
      color: theme.colors.onSurfaceVariant,
    },
    overdueDueDate: {
      color: theme.colors.error,
      fontWeight: '600',
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginBottom: 16,
    },
    errorContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    errorText: {
      ...ExpressiveTypography.bodyLarge,
      color: theme.colors.error,
      textAlign: 'center',
      marginBottom: 16,
    },
  });

  // Get status colors
  const getStatusColors = useCallback((status: Task['status']) => {
    switch (status) {
      case 'todo':
        return {
          backgroundColor: theme.colors.surfaceVariant,
          color: theme.colors.onSurfaceVariant,
        };
      case 'in_progress':
        return {
          backgroundColor: theme.colors.primaryContainer,
          color: theme.colors.onPrimaryContainer,
        };
      case 'done':
        return {
          backgroundColor: theme.colors.secondaryContainer,
          color: theme.colors.onSecondaryContainer,
        };
      case 'archived':
        return {
          backgroundColor: theme.colors.surfaceVariant,
          color: theme.colors.onSurfaceVariant,
        };
      default:
        return {
          backgroundColor: theme.colors.surfaceVariant,
          color: theme.colors.onSurfaceVariant,
        };
    }
  }, [theme]);

  // Get priority colors
  const getPriorityColors = useCallback((priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return {
          backgroundColor: theme.colors.errorContainer,
          color: theme.colors.onErrorContainer,
        };
      case 'medium':
        return {
          backgroundColor: theme.colors.tertiaryContainer,
          color: theme.colors.onTertiaryContainer,
        };
      case 'low':
        return {
          backgroundColor: theme.colors.secondaryContainer,
          color: theme.colors.onSecondaryContainer,
        };
      default:
        return {
          backgroundColor: theme.colors.surfaceVariant,
          color: theme.colors.onSurfaceVariant,
        };
    }
  }, [theme]);

  // Format due date
  const formatDueDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays === -1) return 'Yesterday';
    if (diffDays < 0) return `${Math.abs(diffDays)}d overdue`;
    if (diffDays <= 7) return `${diffDays}d`;
    
    return date.toLocaleDateString();
  }, []);

  // Check if task is overdue
  const isTaskOverdue = useCallback((task: Task) => {
    if (!task.due_date) return false;
    return new Date(task.due_date) < new Date() && task.status !== 'done';
  }, []);

  // Handle task selection
  const handleTaskSelect = useCallback((taskId: string) => {
    setSelectedTasks(prev => {
      if (prev.includes(taskId)) {
        return prev.filter(id => id !== taskId);
      } else {
        return [...prev, taskId];
      }
    });
  }, []);

  // Handle task press
  const handleTaskPress = useCallback((task: Task) => {
    if (onTaskPress) {
      onTaskPress(task);
    }
  }, [onTaskPress]);

  // Handle sort
  const handleSort = useCallback((field: typeof sortBy) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  }, [sortBy, sortOrder, setSortBy, setSortOrder]);

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    if (onRefresh) {
      onRefresh();
    } else {
      await loadTasks(groupId);
    }
  }, [onRefresh, loadTasks, groupId]);

  // Get filtered and sorted tasks
  const filteredTasks = useMemo(() => {
    return getFilteredTasks();
  }, [getFilteredTasks]);

  // Get task statistics
  const taskStats = useMemo(() => {
    const stats = {
      total: filteredTasks.length,
      todo: 0,
      inProgress: 0,
      done: 0,
    };

    filteredTasks.forEach(task => {
      switch (task.status) {
        case 'todo':
          stats.todo++;
          break;
        case 'in_progress':
          stats.inProgress++;
          break;
        case 'done':
          stats.done++;
          break;
      }
    });

    return stats;
  }, [filteredTasks]);

  // Render task row
  const renderTaskRow = useCallback(({ item: task }: { item: Task }) => {
    const isSelected = selectedTasks.includes(task.id);
    const statusColors = getStatusColors(task.status);
    const priorityColors = getPriorityColors(task.priority);
    const isOverdue = isTaskOverdue(task);

    return (
      <TouchableOpacity
        style={[styles.taskRow, isSelected && styles.selectedTaskRow]}
        onPress={() => handleTaskPress(task)}
        activeOpacity={0.7}
      >
        <View style={styles.taskContent}>
          {/* Checkbox */}
          <TouchableOpacity
            style={[styles.checkbox, isSelected && styles.checkedCheckbox]}
            onPress={() => handleTaskSelect(task.id)}
          >
            {isSelected && <Text style={styles.checkmark}>✓</Text>}
          </TouchableOpacity>

          {/* Task Info */}
          <View style={styles.taskInfo}>
            <Text style={styles.taskTitle} numberOfLines={1}>
              {task.title}
            </Text>

            {task.description && (
              <Text style={styles.taskDescription} numberOfLines={2}>
                {task.description}
              </Text>
            )}

            <View style={styles.taskMeta}>
              {/* Status Badge */}
              <View style={[styles.statusBadge, statusColors]}>
                <Text style={[styles.statusText, { color: statusColors.color }]}>
                  {task.status.replace('_', ' ').toUpperCase()}
                </Text>
              </View>

              {/* Priority Badge */}
              <View style={[styles.priorityBadge, priorityColors]}>
                <Text style={[styles.priorityText, { color: priorityColors.color }]}>
                  {task.priority.toUpperCase()}
                </Text>
              </View>

              {/* Due Date */}
              {task.due_date && (
                <Text style={[styles.dueDate, isOverdue && styles.overdueDueDate]}>
                  {formatDueDate(task.due_date)}
                </Text>
              )}
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }, [
    selectedTasks,
    getStatusColors,
    getPriorityColors,
    isTaskOverdue,
    handleTaskPress,
    handleTaskSelect,
    formatDueDate,
    styles,
  ]);

  // Render sort header
  const renderSortHeader = () => (
    <View style={styles.sortContainer}>
      <TouchableOpacity
        style={styles.sortButton}
        onPress={() => handleSort('title')}
      >
        <Text style={[styles.sortText, sortBy === 'title' && styles.activeSortText]}>
          Title {sortBy === 'title' && (sortOrder === 'asc' ? '↑' : '↓')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.sortButton}
        onPress={() => handleSort('priority')}
      >
        <Text style={[styles.sortText, sortBy === 'priority' && styles.activeSortText]}>
          Priority {sortBy === 'priority' && (sortOrder === 'asc' ? '↑' : '↓')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.sortButton}
        onPress={() => handleSort('due_date')}
      >
        <Text style={[styles.sortText, sortBy === 'due_date' && styles.activeSortText]}>
          Due Date {sortBy === 'due_date' && (sortOrder === 'asc' ? '↑' : '↓')}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.sortButton}
        onPress={() => handleSort('updated_at')}
      >
        <Text style={[styles.sortText, sortBy === 'updated_at' && styles.activeSortText]}>
          Updated {sortBy === 'updated_at' && (sortOrder === 'asc' ? '↑' : '↓')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Render error state
  if (error) {
    return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
          <ExpressiveButton
            title="Retry"
            variant="filled"
            onPress={handleRefresh}
          />
        </View>
      </View>
    );
  }

  // Render empty state
  if (!isLoading && filteredTasks.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Task List</Text>
          <Text style={styles.subtitle}>No tasks found</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {tasks.length === 0
              ? 'Create your first task to get started.'
              : 'No tasks match your current filters.'
            }
          </Text>
          {onCreateTask && (
            <ExpressiveButton
              title="Create Task"
              variant="filled"
              onPress={onCreateTask}
            />
          )}
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Task List</Text>
        <Text style={styles.subtitle}>
          {taskStats.total} tasks • {taskStats.todo} to do • {taskStats.inProgress} in progress • {taskStats.done} done
        </Text>
      </View>

      {/* Sort Header */}
      {renderSortHeader()}

      {/* Task List */}
      <FlatList
        style={styles.listContainer}
        data={filteredTasks}
        renderItem={renderTaskRow}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing || isLoading}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
            colors={[theme.colors.primary]}
          />
        }
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={20}
        windowSize={10}
        initialNumToRender={15}
      />
    </View>
  );
};

export default TaskTable;
