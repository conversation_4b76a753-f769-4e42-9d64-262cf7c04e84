import { supabase } from './client';
import { Database } from '../../types/database';
import { StudySession, AnalyticsMetrics, DateRange } from '../../stores/analyticsStore';

type StudySessionRow = Database['public']['Tables']['study_sessions']['Row'];

export class AnalyticsService {
  /**
   * Map database row to StudySession interface
   */
  private mapRowToStudySession(row: StudySessionRow): StudySession {
    return {
      id: row.id,
      user_id: row.user_id,
      subject: row.subject,
      task_name: row.task_name,
      task_type: row.task_type,
      start_time: row.start_time,
      end_time: row.end_time,
      duration: row.duration,
      mode: row.mode,
      phase: row.phase,
      completed: row.completed,
      date: row.date,
      notes: row.notes,
      productivity_rating: row.productivity_rating,
      created_at: row.created_at,
      updated_at: row.updated_at,
    };
  }

  /**
   * Get study sessions for a user within a date range
   */
  async getStudySessions(
    userId: string,
    dateRange?: DateRange
  ): Promise<StudySession[]> {
    try {
      let query = supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .order('start_time', { ascending: false });

      if (dateRange) {
        const startDate = dateRange.start.toISOString().split('T')[0];
        const endDate = dateRange.end.toISOString().split('T')[0];
        query = query.gte('date', startDate).lte('date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching study sessions:', error);
        throw error;
      }

      return data.map(this.mapRowToStudySession);
    } catch (error) {
      console.error('Failed to get study sessions:', error);
      throw error;
    }
  }

  /**
   * Get study sessions by subject
   */
  async getSessionsBySubject(
    userId: string,
    subject: string,
    dateRange?: DateRange
  ): Promise<StudySession[]> {
    try {
      let query = supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('subject', subject)
        .order('start_time', { ascending: false });

      if (dateRange) {
        const startDate = dateRange.start.toISOString().split('T')[0];
        const endDate = dateRange.end.toISOString().split('T')[0];
        query = query.gte('date', startDate).lte('date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching sessions by subject:', error);
        throw error;
      }

      return data.map(this.mapRowToStudySession);
    } catch (error) {
      console.error('Failed to get sessions by subject:', error);
      throw error;
    }
  }

  /**
   * Get completed sessions only
   */
  async getCompletedSessions(
    userId: string,
    dateRange?: DateRange
  ): Promise<StudySession[]> {
    try {
      let query = supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('completed', true)
        .order('start_time', { ascending: false });

      if (dateRange) {
        const startDate = dateRange.start.toISOString().split('T')[0];
        const endDate = dateRange.end.toISOString().split('T')[0];
        query = query.gte('date', startDate).lte('date', endDate);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching completed sessions:', error);
        throw error;
      }

      return data.map(this.mapRowToStudySession);
    } catch (error) {
      console.error('Failed to get completed sessions:', error);
      throw error;
    }
  }

  /**
   * Calculate basic analytics metrics
   */
  async calculateBasicMetrics(
    userId: string,
    dateRange?: DateRange
  ): Promise<AnalyticsMetrics> {
    try {
      const sessions = await this.getStudySessions(userId, dateRange);
      const completedSessions = sessions.filter(session => session.completed);

      // Calculate total study time
      const totalStudyTime = sessions.reduce((sum, session) => sum + session.duration, 0);

      // Calculate average session length
      const averageSessionLength = sessions.length > 0 ? totalStudyTime / sessions.length : 0;

      // Calculate productivity score
      const ratingsSum = sessions
        .filter(session => session.productivity_rating !== null)
        .reduce((sum, session) => sum + (session.productivity_rating || 0), 0);
      const ratingsCount = sessions.filter(session => session.productivity_rating !== null).length;
      const productivityScore = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

      // Calculate subject distribution
      const subjectDistribution = sessions.reduce((acc, session) => {
        acc[session.subject] = (acc[session.subject] || 0) + session.duration;
        return acc;
      }, {} as { [subject: string]: number });

      // Calculate streak (simplified - consecutive days with sessions)
      const streakDays = await this.calculateStreak(userId);

      return {
        totalStudyTime,
        averageSessionLength,
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        productivityScore,
        streakDays,
        currentStreak: streakDays,
        longestStreak: streakDays, // TODO: Implement proper longest streak calculation
        subjectDistribution,
        dailyGoalProgress: 0, // TODO: Implement goal tracking
        weeklyGoalProgress: 0,
        monthlyGoalProgress: 0,
      };
    } catch (error) {
      console.error('Failed to calculate basic metrics:', error);
      throw error;
    }
  }

  /**
   * Calculate study streak
   */
  async calculateStreak(userId: string): Promise<number> {
    try {
      // Get sessions grouped by date for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data, error } = await supabase
        .from('study_sessions')
        .select('date, completed')
        .eq('user_id', userId)
        .eq('completed', true)
        .gte('date', thirtyDaysAgo.toISOString().split('T')[0])
        .order('date', { ascending: false });

      if (error) {
        console.error('Error calculating streak:', error);
        throw error;
      }

      if (!data || data.length === 0) return 0;

      // Group by date and count consecutive days
      const uniqueDates = [...new Set(data.map(session => session.date))].sort().reverse();
      
      let streak = 0;
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // Start counting from today or yesterday
      for (let i = 0; i < uniqueDates.length; i++) {
        const date = uniqueDates[i];
        
        if (i === 0 && (date === today || date === yesterday)) {
          streak = 1;
        } else if (i > 0) {
          const prevDate = new Date(uniqueDates[i - 1]);
          const currDate = new Date(date);
          const diffDays = Math.floor((prevDate.getTime() - currDate.getTime()) / (24 * 60 * 60 * 1000));
          
          if (diffDays === 1) {
            streak++;
          } else {
            break;
          }
        }
      }

      return streak;
    } catch (error) {
      console.error('Failed to calculate streak:', error);
      return 0;
    }
  }

  /**
   * Get daily study time aggregation
   */
  async getDailyStudyTime(
    userId: string,
    dateRange: DateRange
  ): Promise<{ date: string; totalTime: number; sessions: number }[]> {
    try {
      const startDate = dateRange.start.toISOString().split('T')[0];
      const endDate = dateRange.end.toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('study_sessions')
        .select('date, duration, completed')
        .eq('user_id', userId)
        .eq('completed', true)
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: true });

      if (error) {
        console.error('Error fetching daily study time:', error);
        throw error;
      }

      // Group by date and sum duration
      const dailyData = data.reduce((acc, session) => {
        const date = session.date;
        if (!acc[date]) {
          acc[date] = { totalTime: 0, sessions: 0 };
        }
        acc[date].totalTime += session.duration;
        acc[date].sessions += 1;
        return acc;
      }, {} as { [date: string]: { totalTime: number; sessions: number } });

      return Object.entries(dailyData).map(([date, data]) => ({
        date,
        totalTime: data.totalTime,
        sessions: data.sessions,
      }));
    } catch (error) {
      console.error('Failed to get daily study time:', error);
      throw error;
    }
  }

  /**
   * Get subject-wise time distribution
   */
  async getSubjectDistribution(
    userId: string,
    dateRange?: DateRange
  ): Promise<{ subject: string; totalTime: number; sessions: number; percentage: number }[]> {
    try {
      const sessions = await this.getCompletedSessions(userId, dateRange);
      
      if (sessions.length === 0) return [];

      const totalTime = sessions.reduce((sum, session) => sum + session.duration, 0);
      
      const subjectData = sessions.reduce((acc, session) => {
        if (!acc[session.subject]) {
          acc[session.subject] = { totalTime: 0, sessions: 0 };
        }
        acc[session.subject].totalTime += session.duration;
        acc[session.subject].sessions += 1;
        return acc;
      }, {} as { [subject: string]: { totalTime: number; sessions: number } });

      return Object.entries(subjectData)
        .map(([subject, data]) => ({
          subject,
          totalTime: data.totalTime,
          sessions: data.sessions,
          percentage: (data.totalTime / totalTime) * 100,
        }))
        .sort((a, b) => b.totalTime - a.totalTime);
    } catch (error) {
      console.error('Failed to get subject distribution:', error);
      throw error;
    }
  }

  /**
   * Get productivity trends over time
   */
  async getProductivityTrends(
    userId: string,
    dateRange: DateRange
  ): Promise<{ date: string; averageRating: number; sessions: number }[]> {
    try {
      const startDate = dateRange.start.toISOString().split('T')[0];
      const endDate = dateRange.end.toISOString().split('T')[0];

      const { data, error } = await supabase
        .from('study_sessions')
        .select('date, productivity_rating')
        .eq('user_id', userId)
        .eq('completed', true)
        .not('productivity_rating', 'is', null)
        .gte('date', startDate)
        .lte('date', endDate)
        .order('date', { ascending: true });

      if (error) {
        console.error('Error fetching productivity trends:', error);
        throw error;
      }

      // Group by date and calculate average rating
      const dailyRatings = data.reduce((acc, session) => {
        const date = session.date;
        if (!acc[date]) {
          acc[date] = { ratings: [], sessions: 0 };
        }
        if (session.productivity_rating !== null) {
          acc[date].ratings.push(session.productivity_rating);
        }
        acc[date].sessions += 1;
        return acc;
      }, {} as { [date: string]: { ratings: number[]; sessions: number } });

      return Object.entries(dailyRatings).map(([date, data]) => ({
        date,
        averageRating: data.ratings.length > 0 
          ? data.ratings.reduce((sum, rating) => sum + rating, 0) / data.ratings.length 
          : 0,
        sessions: data.sessions,
      }));
    } catch (error) {
      console.error('Failed to get productivity trends:', error);
      throw error;
    }
  }

  /**
   * Subscribe to real-time changes for study sessions
   */
  subscribeToStudySessions(
    userId: string,
    callback: (payload: any) => void
  ) {
    return supabase
      .channel('study_sessions_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'study_sessions',
          filter: `user_id=eq.${userId}`,
        },
        callback
      )
      .subscribe();
  }

  /**
   * Unsubscribe from real-time changes
   */
  unsubscribeFromStudySessions(subscription: any) {
    if (subscription) {
      supabase.removeChannel(subscription);
    }
  }
}

// Create and export a singleton instance
export const analyticsService = new AnalyticsService();
