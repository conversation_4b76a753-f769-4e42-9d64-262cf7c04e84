import { supabase } from '../supabase/client';
import { Subject } from '../../stores/subjectStore';

export interface SubjectAnalytics {
  subjectId: string;
  subjectName: string;
  subjectColor: string;
  totalTime: number;
  sessionCount: number;
  averageSessionLength: number;
  longestSession: number;
  shortestSession: number;
  lastStudied: Date | null;
  streak: number;
  productivityRating: number;
  
  // Time breakdowns
  todayTime: number;
  weekTime: number;
  monthTime: number;
  
  // Session type breakdown
  pomodoroSessions: number;
  stopwatchSessions: number;
  pomodoroTime: number;
  stopwatchTime: number;
  
  // Productivity metrics
  completedSessions: number;
  completionRate: number;
  averageProductivity: number;
  
  // Trend data
  dailyTrend: Array<{ date: string; time: number; sessions: number }>;
  weeklyTrend: Array<{ week: string; time: number; sessions: number }>;
  monthlyTrend: Array<{ month: string; time: number; sessions: number }>;
}

export interface SubjectComparison {
  subject: string;
  color: string;
  percentage: number;
  time: number;
  sessions: number;
  rank: number;
}

export interface StudyPattern {
  hour: number;
  totalTime: number;
  sessionCount: number;
  averageProductivity: number;
}

class SubjectAnalyticsService {
  /**
   * Get comprehensive analytics for a specific subject
   */
  async getSubjectAnalytics(
    userId: string,
    subjectId: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<SubjectAnalytics | null> {
    try {
      // Get subject info
      const { data: subject, error: subjectError } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('id', subjectId)
        .eq('user_id', userId)
        .single();

      if (subjectError || !subject) {
        console.error('Error fetching subject:', subjectError);
        return null;
      }

      // Build date filter
      let dateFilter = '';
      if (dateRange) {
        dateFilter = `and start_time.gte.${dateRange.start.toISOString()} and start_time.lte.${dateRange.end.toISOString()}`;
      }

      // Get all sessions for this subject
      const { data: sessions, error: sessionsError } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('subject', subjectId)
        .eq('completed', true);

      if (sessionsError) {
        console.error('Error fetching sessions:', sessionsError);
        return null;
      }

      if (!sessions || sessions.length === 0) {
        return this.getEmptyAnalytics(subject);
      }

      // Calculate basic metrics
      const totalTime = sessions.reduce((sum, session) => sum + session.duration, 0);
      const sessionCount = sessions.length;
      const averageSessionLength = totalTime / sessionCount;
      const longestSession = Math.max(...sessions.map(s => s.duration));
      const shortestSession = Math.min(...sessions.map(s => s.duration));
      
      const lastStudied = sessions.length > 0
        ? new Date(Math.max(...sessions.map(s => new Date(s.start_time).getTime())))
        : null;

      // Calculate time breakdowns
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - today.getDay());
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

      const todayTime = this.calculateTimeInPeriod(sessions, today, new Date(today.getTime() + 24 * 60 * 60 * 1000));
      const weekTime = this.calculateTimeInPeriod(sessions, weekStart, now);
      const monthTime = this.calculateTimeInPeriod(sessions, monthStart, now);

      // Session type breakdown
      const pomodoroSessions = sessions.filter(s => s.mode === 'pomodoro').length;
      const stopwatchSessions = sessions.filter(s => s.mode === 'stopwatch').length;
      const pomodoroTime = sessions
        .filter(s => s.mode === 'pomodoro')
        .reduce((sum, s) => sum + s.duration, 0);
      const stopwatchTime = sessions
        .filter(s => s.mode === 'stopwatch')
        .reduce((sum, s) => sum + s.duration, 0);

      // Productivity metrics
      const completedSessions = sessions.filter(s => s.completed).length;
      const completionRate = (completedSessions / sessionCount) * 100;
      const ratingsSum = sessions
        .filter(s => s.productivity_rating)
        .reduce((sum, s) => sum + (s.productivity_rating || 0), 0);
      const ratingsCount = sessions.filter(s => s.productivity_rating).length;
      const averageProductivity = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

      // Calculate streak
      const streak = this.calculateStreak(sessions);

      // Generate trend data
      const dailyTrend = this.generateDailyTrend(sessions, 30);
      const weeklyTrend = this.generateWeeklyTrend(sessions, 12);
      const monthlyTrend = this.generateMonthlyTrend(sessions, 12);

      return {
        subjectId: subject.id,
        subjectName: subject.name,
        subjectColor: subject.color,
        totalTime,
        sessionCount,
        averageSessionLength,
        longestSession,
        shortestSession,
        lastStudied,
        streak,
        productivityRating: averageProductivity,
        
        todayTime,
        weekTime,
        monthTime,
        
        pomodoroSessions,
        stopwatchSessions,
        pomodoroTime,
        stopwatchTime,
        
        completedSessions,
        completionRate,
        averageProductivity,
        
        dailyTrend,
        weeklyTrend,
        monthlyTrend,
      };
    } catch (error) {
      console.error('Error getting subject analytics:', error);
      return null;
    }
  }

  /**
   * Get analytics for all subjects with comparison data
   */
  async getAllSubjectsAnalytics(
    userId: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<SubjectComparison[]> {
    try {
      // Get all subjects
      const { data: subjects, error: subjectsError } = await supabase
        .from('userSubjects')
        .select('*')
        .eq('user_id', userId);

      if (subjectsError || !subjects) {
        console.error('Error fetching subjects:', subjectsError);
        return [];
      }

      // Get analytics for each subject
      const analyticsPromises = subjects.map(subject =>
        this.getSubjectAnalytics(userId, subject.id, dateRange)
      );

      const analyticsResults = await Promise.all(analyticsPromises);
      const validAnalytics = analyticsResults.filter(Boolean) as SubjectAnalytics[];

      // Calculate total time across all subjects
      const totalTime = validAnalytics.reduce((sum, analytics) => sum + analytics.totalTime, 0);

      // Create comparison data
      const comparisons: SubjectComparison[] = validAnalytics
        .map(analytics => ({
          subject: analytics.subjectName,
          color: analytics.subjectColor,
          percentage: totalTime > 0 ? (analytics.totalTime / totalTime) * 100 : 0,
          time: analytics.totalTime,
          sessions: analytics.sessionCount,
          rank: 0, // Will be set after sorting
        }))
        .sort((a, b) => b.time - a.time)
        .map((comparison, index) => ({
          ...comparison,
          rank: index + 1,
        }));

      return comparisons;
    } catch (error) {
      console.error('Error getting all subjects analytics:', error);
      return [];
    }
  }

  /**
   * Get study patterns by hour of day
   */
  async getStudyPatterns(
    userId: string,
    subjectId?: string,
    dateRange?: { start: Date; end: Date }
  ): Promise<StudyPattern[]> {
    try {
      let query = supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('completed', true);

      if (subjectId) {
        query = query.eq('subject', subjectId);
      }

      if (dateRange) {
        query = query
          .gte('start_time', dateRange.start.toISOString())
          .lte('start_time', dateRange.end.toISOString());
      }

      const { data: sessions, error } = await query;

      if (error || !sessions) {
        console.error('Error fetching sessions for patterns:', error);
        return [];
      }

      // Group sessions by hour
      const hourlyData: { [hour: number]: { time: number; sessions: number; ratings: number[] } } = {};

      sessions.forEach(session => {
        const hour = new Date(session.start_time).getHours();
        if (!hourlyData[hour]) {
          hourlyData[hour] = { time: 0, sessions: 0, ratings: [] };
        }
        
        hourlyData[hour].time += session.duration;
        hourlyData[hour].sessions += 1;
        
        if (session.productivity_rating) {
          hourlyData[hour].ratings.push(session.productivity_rating);
        }
      });

      // Convert to array format
      const patterns: StudyPattern[] = [];
      for (let hour = 0; hour < 24; hour++) {
        const data = hourlyData[hour] || { time: 0, sessions: 0, ratings: [] };
        const averageProductivity = data.ratings.length > 0
          ? data.ratings.reduce((sum, rating) => sum + rating, 0) / data.ratings.length
          : 0;

        patterns.push({
          hour,
          totalTime: data.time,
          sessionCount: data.sessions,
          averageProductivity,
        });
      }

      return patterns;
    } catch (error) {
      console.error('Error getting study patterns:', error);
      return [];
    }
  }

  /**
   * Calculate time spent in a specific period
   */
  private calculateTimeInPeriod(sessions: any[], start: Date, end: Date): number {
    return sessions
      .filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= start && sessionDate < end;
      })
      .reduce((sum, session) => sum + session.duration, 0);
  }

  /**
   * Calculate study streak
   */
  private calculateStreak(sessions: any[]): number {
    if (sessions.length === 0) return 0;

    const dates = Array.from(new Set(
      sessions.map(session => {
        const date = new Date(session.start_time);
        date.setHours(0, 0, 0, 0);
        return date.getTime();
      })
    )).sort((a, b) => b - a);

    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < dates.length; i++) {
      const expectedDate = new Date(today);
      expectedDate.setDate(expectedDate.getDate() - i);

      if (dates[i] === expectedDate.getTime()) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  /**
   * Generate daily trend data
   */
  private generateDailyTrend(sessions: any[], days: number): Array<{ date: string; time: number; sessions: number }> {
    const trend = [];
    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      date.setHours(0, 0, 0, 0);

      const nextDay = new Date(date);
      nextDay.setDate(date.getDate() + 1);

      const dayTime = this.calculateTimeInPeriod(sessions, date, nextDay);
      const daySessions = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= date && sessionDate < nextDay;
      }).length;

      trend.push({
        date: date.toISOString().split('T')[0],
        time: dayTime,
        sessions: daySessions,
      });
    }

    return trend;
  }

  /**
   * Generate weekly trend data
   */
  private generateWeeklyTrend(sessions: any[], weeks: number): Array<{ week: string; time: number; sessions: number }> {
    const trend = [];
    const today = new Date();

    for (let i = weeks - 1; i >= 0; i--) {
      const weekStart = new Date(today);
      weekStart.setDate(today.getDate() - (today.getDay() + i * 7));
      weekStart.setHours(0, 0, 0, 0);

      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 7);

      const weekTime = this.calculateTimeInPeriod(sessions, weekStart, weekEnd);
      const weekSessions = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= weekStart && sessionDate < weekEnd;
      }).length;

      trend.push({
        week: `${weekStart.getMonth() + 1}/${weekStart.getDate()}`,
        time: weekTime,
        sessions: weekSessions,
      });
    }

    return trend;
  }

  /**
   * Generate monthly trend data
   */
  private generateMonthlyTrend(sessions: any[], months: number): Array<{ month: string; time: number; sessions: number }> {
    const trend = [];
    const today = new Date();

    for (let i = months - 1; i >= 0; i--) {
      const monthStart = new Date(today.getFullYear(), today.getMonth() - i, 1);
      const monthEnd = new Date(today.getFullYear(), today.getMonth() - i + 1, 1);

      const monthTime = this.calculateTimeInPeriod(sessions, monthStart, monthEnd);
      const monthSessions = sessions.filter(session => {
        const sessionDate = new Date(session.start_time);
        return sessionDate >= monthStart && sessionDate < monthEnd;
      }).length;

      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                         'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      trend.push({
        month: monthNames[monthStart.getMonth()],
        time: monthTime,
        sessions: monthSessions,
      });
    }

    return trend;
  }

  /**
   * Get empty analytics for subjects with no sessions
   */
  private getEmptyAnalytics(subject: any): SubjectAnalytics {
    return {
      subjectId: subject.id,
      subjectName: subject.name,
      subjectColor: subject.color,
      totalTime: 0,
      sessionCount: 0,
      averageSessionLength: 0,
      longestSession: 0,
      shortestSession: 0,
      lastStudied: null,
      streak: 0,
      productivityRating: 0,
      
      todayTime: 0,
      weekTime: 0,
      monthTime: 0,
      
      pomodoroSessions: 0,
      stopwatchSessions: 0,
      pomodoroTime: 0,
      stopwatchTime: 0,
      
      completedSessions: 0,
      completionRate: 0,
      averageProductivity: 0,
      
      dailyTrend: [],
      weeklyTrend: [],
      monthlyTrend: [],
    };
  }
}

// Export singleton instance
export const subjectAnalyticsService = new SubjectAnalyticsService();
