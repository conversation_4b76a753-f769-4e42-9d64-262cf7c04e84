// Database type definitions for Supabase
// This file will be populated with generated types from Supabase

export interface Database {
  public: {
    Tables: {
      // User profiles
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          daily_target_seconds: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          daily_target_seconds?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          daily_target_seconds?: number;
          created_at?: string;
          updated_at?: string;
        };
      };

      // Study sessions
      study_sessions: {
        Row: {
          id: string;
          user_id: string;
          subject: string;
          task_name: string | null;
          task_type: string | null;
          start_time: string;
          end_time: string | null;
          duration: number;
          mode: 'pomodoro' | 'stopwatch';
          phase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
          completed: boolean;
          date: string;
          notes: string | null;
          productivity_rating: number | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          subject: string;
          task_name?: string | null;
          task_type?: string | null;
          start_time: string;
          end_time?: string | null;
          duration: number;
          mode: 'pomodoro' | 'stopwatch';
          phase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
          completed?: boolean;
          date: string;
          notes?: string | null;
          productivity_rating?: number | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          subject?: string;
          task_name?: string | null;
          task_type?: string | null;
          start_time?: string;
          end_time?: string | null;
          duration?: number;
          mode?: 'pomodoro' | 'stopwatch';
          phase?: 'work' | 'shortBreak' | 'longBreak' | 'pause';
          completed?: boolean;
          date?: string;
          notes?: string | null;
          productivity_rating?: number | null;
          created_at?: string;
          updated_at?: string;
        };
      };

      // User subjects
      userSubjects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          color: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          color: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          color?: string;
          created_at?: string;
          updated_at?: string;
        };
      };

      // Tasks/Todos
      todos: {
        Row: {
          id: string;
          user_id: string;
          group_id: string | null;
          title: string;
          description: string | null;
          status: 'todo' | 'in_progress' | 'done' | 'archived';
          priority: 'low' | 'medium' | 'high';
          column_id: string;
          due_date: string | null;
          assigned_to: string | null;
          assigned_to_photo_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          group_id?: string | null;
          title: string;
          description?: string | null;
          status?: 'todo' | 'in_progress' | 'done' | 'archived';
          priority?: 'low' | 'medium' | 'high';
          column_id: string;
          due_date?: string | null;
          assigned_to?: string | null;
          assigned_to_photo_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          group_id?: string | null;
          title?: string;
          description?: string | null;
          status?: 'todo' | 'in_progress' | 'done' | 'archived';
          priority?: 'low' | 'medium' | 'high';
          column_id?: string;
          due_date?: string | null;
          assigned_to?: string | null;
          assigned_to_photo_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };

      // Groups
      groups: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          created_by: string;
          members: string[];
          is_public: boolean;
          invite_code: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          created_by: string;
          members?: string[];
          is_public?: boolean;
          invite_code?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          created_by?: string;
          members?: string[];
          is_public?: boolean;
          invite_code?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };

      // Mock tests
      mock_tests: {
        Row: {
          id: string;
          user_id: string;
          test_name: string;
          test_data: any; // JSON data
          score: number;
          total_score: number;
          percentage: number;
          analysis_data: any; // JSON analysis
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          test_name: string;
          test_data: any;
          score: number;
          total_score: number;
          percentage: number;
          analysis_data?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          test_name?: string;
          test_data?: any;
          score?: number;
          total_score?: number;
          percentage?: number;
          analysis_data?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
